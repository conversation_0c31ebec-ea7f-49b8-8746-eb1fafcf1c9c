<?php
// Get current page for active navigation
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-dark sticky-top" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);">
    <div class="container">
        <!-- Brand/Logo -->
        <a class="navbar-brand d-flex align-items-center" href="home.php">
            <img src="assets/png-coat-of-arms.png" alt="PNG Coat of Arms" style="height: 40px; margin-right: 10px;" onerror="this.style.display='none'">
            <div>
                <div style="font-size: 1.1rem; font-weight: 600; line-height: 1.2;">
                    Solomon Islands Student Registration
                </div>
                <div style="font-size: 0.75rem; opacity: 0.8;">
                    PNG Higher Education Program
                </div>
            </div>
        </a>

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'home.php' || $current_page == 'index.php') ? 'active' : ''; ?>"
                       href="home.php">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page == 'application.php' ? 'active' : ''; ?>"
                       href="application.php">
                        <i class="fas fa-user-plus me-1"></i>Register Now
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="infoDropdown" role="button" 
                       data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-info-circle me-1"></i>Information
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="about.php">
                            <i class="fas fa-university me-2"></i>About Program
                        </a></li>
                        <li><a class="dropdown-item" href="requirements.php">
                            <i class="fas fa-list-check me-2"></i>Requirements
                        </a></li>
                        <li><a class="dropdown-item" href="institutions.php">
                            <i class="fas fa-graduation-cap me-2"></i>Institutions
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="contact.php">
                            <i class="fas fa-phone me-2"></i>Contact Us
                        </a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page == 'status.php' ? 'active' : ''; ?>" 
                       href="status.php">
                        <i class="fas fa-search me-1"></i>Check Status
                    </a>
                </li>
            </ul>

            <!-- Right Side Navigation -->
            <ul class="navbar-nav">
                <!-- Language Selector -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" 
                       data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-globe me-1"></i>EN
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="setLanguage('en')">
                            <img src="assets/flag-uk.png" alt="English" style="width: 20px; margin-right: 8px;" onerror="this.style.display='none'">
                            English
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="setLanguage('si')">
                            <img src="assets/solomon-flag.png" alt="Solomon Islands" style="width: 20px; margin-right: 8px;" onerror="this.style.display='none'">
                            Solomon Islands Pijin
                        </a></li>
                    </ul>
                </li>

                <!-- Admin Login -->
                <li class="nav-item">
                    <a class="nav-link btn btn-outline-light btn-sm ms-2 px-3" 
                       href="admin/login.php" 
                       style="border-radius: 20px; transition: all 0.3s ease;">
                        <i class="fas fa-user-shield me-1"></i>Admin Login
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Department Header (Optional - can be shown on specific pages) -->
<div class="bg-light border-bottom py-2 d-none" id="departmentHeader">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <small class="text-muted">
                    <strong>Department of Higher Education Research Science & Technology</strong> - 
                    Independent State of Papua New Guinea
                </small>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom Navbar Styles */
.navbar-brand:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.nav-link {
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 0 2px;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(220, 53, 69, 0.2);
    color: #fff !important;
    font-weight: 600;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

.dropdown-item {
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 2px 5px;
}

.dropdown-item:hover {
    background-color: var(--bs-primary);
    color: white;
    transform: translateX(5px);
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .navbar-brand div:first-child {
        font-size: 1rem;
    }
    
    .navbar-brand div:last-child {
        font-size: 0.7rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 2px 0;
    }
    
    .btn-outline-light {
        margin-top: 10px;
        margin-left: 0 !important;
    }
}

/* Admin Login Button Hover Effect */
.btn-outline-light:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}
</style>

<script>
// Language switching function
function setLanguage(lang) {
    // Store language preference
    localStorage.setItem('preferred_language', lang);
    
    // Update language indicator
    const langDropdown = document.getElementById('languageDropdown');
    if (lang === 'en') {
        langDropdown.innerHTML = '<i class="fas fa-globe me-1"></i>EN';
    } else if (lang === 'si') {
        langDropdown.innerHTML = '<i class="fas fa-globe me-1"></i>SI';
    }
    
    // Here you can add actual language switching logic
    console.log('Language switched to:', lang);
}

// Initialize language on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedLang = localStorage.getItem('preferred_language') || 'en';
    setLanguage(savedLang);
});
</script>
