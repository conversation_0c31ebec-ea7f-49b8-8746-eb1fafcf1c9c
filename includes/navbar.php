<?php
// Get current page for active navigation
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-dark sticky-top" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);">
    <div class="container">
        <!-- Brand/Logo -->
        <a class="navbar-brand d-flex align-items-center" href="home.php">
            <img src="assets/png-coat-of-arms.png" alt="PNG Coat of Arms" style="height: 40px; margin-right: 10px;" onerror="this.style.display='none'">
            <div>
                <div style="font-size: 1.1rem; font-weight: 600; line-height: 1.2;">
                    Solomon Islands Student Registration
                </div>
                <div style="font-size: 0.75rem; opacity: 0.8;">
                    PNG Higher Education Program
                </div>
            </div>
        </a>

        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'home.php' || $current_page == 'index.php') ? 'active' : ''; ?>"
                       href="home.php">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page == 'application.php' ? 'active' : ''; ?>"
                       href="application.php">
                        <i class="fas fa-user-plus me-1"></i>Register
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page == 'status.php' ? 'active' : ''; ?>"
                       href="status.php">
                        <i class="fas fa-search me-1"></i>Status
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page == 'contact.php' ? 'active' : ''; ?>"
                       href="contact.php">
                        <i class="fas fa-phone me-1"></i>Contact
                    </a>
                </li>
            </ul>

            <!-- Right Side Navigation -->
            <ul class="navbar-nav">
                <!-- Admin Login -->
                <li class="nav-item">
                    <a class="nav-link btn btn-outline-light btn-sm ms-2 px-3"
                       href="admin/login.php"
                       style="border-radius: 20px; transition: all 0.3s ease;">
                        <i class="fas fa-user-shield me-1"></i>Admin
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>



<style>
/* Custom Navbar Styles */
.navbar-brand:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.nav-link {
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 0 2px;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(220, 53, 69, 0.2);
    color: #fff !important;
    font-weight: 600;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

.dropdown-item {
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 2px 5px;
}

.dropdown-item:hover {
    background-color: var(--bs-primary);
    color: white;
    transform: translateX(5px);
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .navbar-brand div:first-child {
        font-size: 0.95rem;
    }

    .navbar-brand div:last-child {
        font-size: 0.7rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 2px 0;
        border-radius: 8px;
    }

    .navbar-collapse {
        background-color: rgba(44, 62, 80, 0.95);
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 10px;
    }

    .btn-outline-light {
        margin-top: 10px;
        margin-left: 0 !important;
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .navbar-brand div:first-child {
        font-size: 0.85rem;
    }

    .navbar-brand div:last-child {
        font-size: 0.65rem;
    }
}

/* Admin Login Button Hover Effect */
.btn-outline-light:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}
</style>


