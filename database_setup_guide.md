# Database Setup Guide for Solomon Islands Scholarship Application

## Problem
The original `schema.sql` file contains `CREATE DATABASE` commands that require database creation privileges, which are typically not available in shared hosting environments.

## Solution
Use the `schema_import.sql` file instead, which is designed for importing into your existing database.

## Step-by-Step Instructions

### Method 1: Using phpMyAdmin (Recommended)

1. **Login to your hosting control panel** (cPanel, Plesk, etc.)

2. **Open phpMyAdmin**
   - Look for "phpMyAdmin" in your hosting control panel
   - Click to open it

3. **Select your database**
   - In the left sidebar, click on your database: `u787474055_solomonislands`
   - This will open your database

4. **Import the SQL file**
   - Click on the "Import" tab at the top
   - Click "Choose File" or "Browse"
   - Select the `schema_import.sql` file from your computer
   - Leave all other settings as default
   - Click "Go" or "Import"

5. **Verify the import**
   - After import, you should see 4 new tables:
     - `applications`
     - `admin_users`
     - `admin_sessions`
     - `application_status_log`

### Method 2: Manual Table Creation (Alternative)

If the import still fails, you can create tables manually:

1. **Open phpMyAdmin and select your database**

2. **Click "SQL" tab**

3. **Copy and paste each CREATE TABLE statement one by one:**

#### Step 1: Create applications table
```sql
CREATE TABLE IF NOT EXISTS applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_code VARCHAR(20) NOT NULL,
    selection_status ENUM('Selected', 'Pending', 'Rejected', 'Under Review') NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    email VARCHAR(100),
    phone VARCHAR(20),
    province_of_origin SET('Central', 'Choiseul', 'Guadalcanal', 'Isabel', 'Makira-Ulawa', 'Malaita', 'Rennell and Bellona', 'Temotu', 'Western', 'Honiara'),
    current_residential_province SET('Central', 'Choiseul', 'Guadalcanal', 'Isabel', 'Makira-Ulawa', 'Malaita', 'Rennell and Bellona', 'Temotu', 'Western', 'Honiara'),
    mode_of_transport_to_honiara ENUM('Sea', 'Land', 'Air') NOT NULL,
    honiara_pom_route VARCHAR(100),
    hei_name SET('University of Papua New Guinea (UPNG)', 'University of Technology (UOT)', 'Papua New Guinea University of Natural Resources and Environment (PNGUNRE)', 'Divine Word University (DWU)', 'Pacific Adventist University (PAU)', 'University of Goroka (UOG)') NOT NULL,
    png_province ENUM('National Capital District (NCD)', 'Morobe', 'Western Highlands', 'Eastern Highlands', 'Madang', 'East New Britain', 'West New Britain', 'Manus', 'New Ireland', 'Bougainville', 'Gulf', 'Central', 'Milne Bay', 'Oro', 'Southern Highlands', 'Hela', 'Jiwaka', 'Chimbu', 'Western', 'Enga') NOT NULL,
    travel_question ENUM('sea-air-air', 'land-air-air', 'air-direct', 'sea-land-air', 'other'),
    estimated_cost DECIMAL(10,2),
    academic_qualifications TEXT,
    course_of_study VARCHAR(200),
    motivation_letter TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_student_code (student_code),
    INDEX idx_selection_status (selection_status),
    INDEX idx_created_at (created_at)
);
```

#### Step 2: Create admin_users table
```sql
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    full_name VARCHAR(100),
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### Step 3: Insert default admin user
```sql
INSERT IGNORE INTO admin_users (username, password_hash, email, full_name) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'System Administrator');
```

#### Step 4: Create admin_sessions table
```sql
CREATE TABLE IF NOT EXISTS admin_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
);
```

#### Step 5: Create application_status_log table
```sql
CREATE TABLE IF NOT EXISTS application_status_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    old_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    changed_by INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES admin_users(id) ON DELETE SET NULL
);
```

### Method 3: Using Database Management Tool

If your hosting provider offers other database management tools:

1. **Look for "MySQL Databases" or "Database Manager"** in your control panel
2. **Select your database**: `u787474055_solomonislands`
3. **Import the `schema_import.sql` file**

## Verification

After successful import, you should have these tables:

1. **applications** - Stores scholarship applications
2. **admin_users** - Admin user accounts (default: admin/admin123)
3. **admin_sessions** - Admin login sessions
4. **application_status_log** - Status change history

## Default Admin Credentials

- **Username**: `admin`
- **Password**: `admin123`
- **⚠️ Important**: Change these credentials immediately after first login!

## Troubleshooting

### If you get "Table already exists" errors:
- This is normal if you're re-running the import
- The `IF NOT EXISTS` clauses prevent errors

### If you get foreign key constraint errors:
- Run the CREATE TABLE statements in the order provided
- Make sure the `admin_users` table is created before `admin_sessions`
- Make sure the `applications` table is created before `application_status_log`

### If you get permission errors:
- Contact your hosting provider
- They may need to grant additional permissions
- Some hosts require database operations through their control panel only

## Next Steps

1. **Import the database** using one of the methods above
2. **Test the application** by visiting `index.php`
3. **Test admin login** by visiting `admin/login.php`
4. **Change default admin password** immediately
5. **Delete setup files** (`fix_env.php`, `setup.php`) for security

## Support

If you continue having issues:
1. Check your hosting provider's documentation for database import procedures
2. Contact your hosting provider's support team
3. They can help with database permissions and import procedures
