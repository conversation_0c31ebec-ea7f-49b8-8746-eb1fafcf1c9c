<?php
/**
 * Database Structure Check
 * Check if the applications table exists and has the correct structure
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🗄️ Database Structure Check</h1>";

try {
    // Get database connection
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<div style='color: green; font-weight: bold;'>✅ Database connection successful</div>";
    echo "<div>Database: u787474055_solomonislands</div>";
    echo "<div>Host: localhost</div>";
    
    // Check if applications table exists
    echo "<h2>Step 1: Check Applications Table</h2>";
    $result = $conn->query("SHOW TABLES LIKE 'applications'");
    
    if ($result->num_rows > 0) {
        echo "<div style='color: green; font-weight: bold;'>✅ Applications table exists</div>";
        
        // Show table structure
        echo "<h3>Current Table Structure:</h3>";
        $structure = $conn->query("DESCRIBE applications");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $existing_fields = [];
        while ($row = $structure->fetch_assoc()) {
            $existing_fields[] = $row['Field'];
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "<td>{$row['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for required fields
        echo "<h3>Required Fields Check:</h3>";
        $required_fields = [
            'id' => 'Primary key',
            'student_code' => 'Student identification',
            'full_name' => 'Student name',
            'province_of_origin' => 'Province data',
            'mode_of_transport_to_honiara' => 'Transport method',
            'png_province' => 'PNG province',
            'academic_qualifications' => 'Academic info',
            'motivation_letter' => 'Motivation letter',
            'created_at' => 'Timestamp',
            'ip_address' => 'User IP',
            'user_agent' => 'Browser info'
        ];
        
        foreach ($required_fields as $field => $description) {
            if (in_array($field, $existing_fields)) {
                echo "<div style='color: green;'>✅ $field - $description</div>";
            } else {
                echo "<div style='color: red;'>❌ $field - $description (MISSING)</div>";
            }
        }
        
        // Count existing records
        echo "<h3>Current Data:</h3>";
        $count_result = $conn->query("SELECT COUNT(*) as count FROM applications");
        $count = $count_result->fetch_assoc()['count'];
        echo "<div style='color: blue; font-weight: bold;'>📊 Total applications: $count</div>";
        
        if ($count > 0) {
            echo "<h4>Recent Applications:</h4>";
            $recent = $conn->query("SELECT id, student_code, full_name, created_at FROM applications ORDER BY created_at DESC LIMIT 5");
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Student Code</th><th>Full Name</th><th>Created At</th></tr>";
            while ($row = $recent->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['student_code']}</td>";
                echo "<td>{$row['full_name']}</td>";
                echo "<td>{$row['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<div style='color: red; font-weight: bold;'>❌ Applications table does not exist!</div>";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>Creating Applications Table...</h3>";
        
        // Create the applications table
        $create_table_sql = "
        CREATE TABLE applications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_code VARCHAR(50) NOT NULL,
            selection_status VARCHAR(50) DEFAULT 'pending',
            full_name VARCHAR(100) NOT NULL,
            date_of_birth DATE NULL,
            gender VARCHAR(20) NULL,
            email VARCHAR(100) NULL,
            phone VARCHAR(20) NULL,
            province_of_origin TEXT NULL,
            current_residential_province TEXT NULL,
            mode_of_transport_to_honiara VARCHAR(50) NOT NULL,
            honiara_pom_route VARCHAR(100) NULL,
            hei_name TEXT NULL,
            png_province VARCHAR(50) NOT NULL,
            travel_question TEXT NULL,
            estimated_cost DECIMAL(10,2) NULL,
            academic_qualifications TEXT NOT NULL,
            course_of_study VARCHAR(200) NULL,
            motivation_letter TEXT NOT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_student_code (student_code),
            INDEX idx_created_at (created_at),
            INDEX idx_selection_status (selection_status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($create_table_sql)) {
            echo "<div style='color: green; font-weight: bold;'>✅ Applications table created successfully!</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>❌ Failed to create applications table: " . $conn->error . "</div>";
        }
        echo "</div>";
    }
    
    // Test database write permissions
    echo "<h2>Step 2: Test Database Write Permissions</h2>";
    
    try {
        // Try to insert a test record
        $test_sql = "INSERT INTO applications (
            student_code, full_name, province_of_origin, 
            mode_of_transport_to_honiara, png_province, 
            academic_qualifications, motivation_letter,
            ip_address, user_agent
        ) VALUES (
            'TEST_" . time() . "', 'Test User', 'Central', 
            'Air', 'National Capital District', 
            'Test qualifications', 'Test motivation',
            'test_ip', 'test_agent'
        )";
        
        if ($conn->query($test_sql)) {
            $test_id = $conn->insert_id;
            echo "<div style='color: green; font-weight: bold;'>✅ Database write test successful (ID: $test_id)</div>";
            
            // Clean up test record
            $conn->query("DELETE FROM applications WHERE id = $test_id");
            echo "<div style='color: blue;'>🧹 Test record cleaned up</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>❌ Database write test failed: " . $conn->error . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red; font-weight: bold;'>❌ Database write test error: " . $e->getMessage() . "</div>";
    }
    
    // Check form field mapping
    echo "<h2>Step 3: Form Field Mapping Check</h2>";
    
    $form_fields = [
        'student_code' => 'Student Code (from Step 1)',
        'full_name' => 'Full Name (from Step 1)', 
        'province_of_origin' => 'Province of Origin (from Step 2)',
        'mode_of_transport_to_honiara' => 'Mode of Transport (from Step 3)',
        'png_province' => 'PNG Province (from Step 3)',
        'academic_qualifications' => 'Academic Qualifications (from Step 4)',
        'course_of_study' => 'Course of Study (from Step 4)',
        'motivation_letter' => 'Motivation Letter (from Step 5)'
    ];
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Form Field to Database Mapping:</h4>";
    foreach ($form_fields as $field => $description) {
        if (in_array($field, $existing_fields)) {
            echo "<div style='color: green;'>✅ $field → $description</div>";
        } else {
            echo "<div style='color: red;'>❌ $field → $description (MISSING FROM DB)</div>";
        }
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold;'>❌ Database connection failed: " . $e->getMessage() . "</div>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Database Connection Details:</h3>";
    echo "<div>Host: localhost</div>";
    echo "<div>Database: u787474055_solomonislands</div>";
    echo "<div>Username: u787474055_solomonislands</div>";
    echo "<div>Password: [Using Blackpanther707@707]</div>";
    echo "</div>";
}

echo "<h2>🎯 Next Steps</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>To test form submission:</h4>";
echo "<ol>";
echo "<li><a href='debug_submit.php' target='_blank'>Go to Debug Submit page</a> - Test form submission with detailed logging</li>";
echo "<li><a href='application.php' target='_blank'>Go to Main Application Form</a> - Test the actual form</li>";
echo "<li>Fill out all required fields and submit</li>";
echo "<li>Check for any error messages or success confirmations</li>";
echo "</ol>";
echo "</div>";
?>
