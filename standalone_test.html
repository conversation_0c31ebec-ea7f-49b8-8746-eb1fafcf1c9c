<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Standalone Navigation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-step { 
            display: none; 
            border: 2px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .form-step.active { 
            display: block; 
            border-color: #007bff;
            background: #ffffff;
        }
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-width: 300px;
            z-index: 1000;
            font-size: 12px;
        }
        .console-log {
            background: #000;
            color: #0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <h6>🔍 Debug Info</h6>
        <div id="debugOutput">Loading...</div>
        <div class="console-log" id="consoleLog">Console ready...</div>
        <button onclick="testAllSteps()" class="btn btn-sm btn-primary mt-2">Test All Steps</button>
    </div>

    <div class="container py-4">
        <h1>🔧 Standalone Navigation Test</h1>
        <p class="alert alert-info">
            <strong>This test has JavaScript built-in (not external file)</strong><br>
            If this works, the problem is with the external script.js file.
        </p>

        <!-- Progress Bar -->
        <div class="progress mb-4">
            <div class="progress-bar" id="progressBar" style="width: 20%"></div>
        </div>

        <!-- Form -->
        <form id="scholarshipForm">
            <!-- Step 1 -->
            <div class="form-step active" data-step="1">
                <h4>Step 1: Basic Info</h4>
                <input type="text" class="form-control mb-3" placeholder="Student Code" required>
                <input type="text" class="form-control" placeholder="Full Name" required>
            </div>

            <!-- Step 2 -->
            <div class="form-step" data-step="2">
                <h4>Step 2: Location</h4>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Central" id="central">
                    <label class="form-check-label" for="central">Central</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Honiara" id="honiara">
                    <label class="form-check-label" for="honiara">Honiara</label>
                </div>
                <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                    Please select at least one province.
                </div>
            </div>

            <!-- Step 3 -->
            <div class="form-step" data-step="3">
                <h4>Step 3: Travel</h4>
                <select class="form-select" required>
                    <option value="">Choose transport...</option>
                    <option value="Air">Air</option>
                    <option value="Sea">Sea</option>
                </select>
            </div>

            <!-- Step 4 -->
            <div class="form-step" data-step="4">
                <h4>Step 4: Academic</h4>
                <textarea class="form-control" placeholder="Academic qualifications" required></textarea>
            </div>

            <!-- Step 5 -->
            <div class="form-step" data-step="5">
                <h4>Step 5: Final</h4>
                <textarea class="form-control mb-3" placeholder="Motivation letter" required></textarea>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" required>
                    <label class="form-check-label">I agree to terms</label>
                </div>
            </div>

            <!-- Navigation -->
            <div class="d-flex justify-content-between mt-4">
                <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">Previous</button>
                <button type="button" class="btn btn-primary" id="nextBtn">Next</button>
                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">Submit</button>
            </div>
        </form>
    </div>

    <script>
        // Built-in JavaScript (not external file)
        console.log('🚀 Standalone test starting...');

        // Global state
        let currentStep = 1;
        const totalSteps = 5;
        let steps, nextBtn, prevBtn, submitBtn, progressBar;

        // Log function
        function log(message) {
            console.log(message);
            const logElement = document.getElementById('consoleLog');
            const time = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${time}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Update debug info
        function updateDebug() {
            const debugOutput = document.getElementById('debugOutput');
            const activeStep = document.querySelector('.form-step.active');
            
            debugOutput.innerHTML = `
                <strong>Current Step:</strong> ${currentStep}/${totalSteps}<br>
                <strong>Active Step:</strong> ${activeStep ? activeStep.dataset.step : 'None'}<br>
                <strong>Steps Found:</strong> ${steps ? steps.length : 'N/A'}<br>
                <strong>Next Btn:</strong> ${nextBtn ? '✅' : '❌'}<br>
                <strong>Event Listeners:</strong> ${nextBtn && nextBtn.onclick ? '✅' : '❌'}
            `;
        }

        // Show specific step
        function showStep(stepNumber) {
            log(`📍 showStep(${stepNumber}) called`);
            
            if (!steps) {
                log('❌ Steps not found!');
                return;
            }

            // Hide all steps
            steps.forEach((step, index) => {
                step.classList.remove('active');
                log(`👻 Hiding step ${index + 1}`);
            });

            // Show target step
            if (steps[stepNumber - 1]) {
                steps[stepNumber - 1].classList.add('active');
                currentStep = stepNumber;
                log(`✨ Showing step ${stepNumber}`);
            } else {
                log(`❌ Step ${stepNumber} not found`);
                return;
            }

            // Update buttons
            if (prevBtn) {
                prevBtn.style.display = stepNumber > 1 ? 'block' : 'none';
            }
            
            if (nextBtn && submitBtn) {
                if (stepNumber === totalSteps) {
                    nextBtn.style.display = 'none';
                    submitBtn.style.display = 'block';
                } else {
                    nextBtn.style.display = 'block';
                    submitBtn.style.display = 'none';
                }
            }

            // Update progress
            if (progressBar) {
                const percentage = (stepNumber / totalSteps) * 100;
                progressBar.style.width = percentage + '%';
                log(`📊 Progress: ${percentage}%`);
            }

            updateDebug();
            log(`🎉 Step ${stepNumber} is now active`);
        }

        // Simple validation
        function validateStep() {
            log(`🔍 Validating step ${currentStep}`);
            
            const currentStepElement = steps[currentStep - 1];
            const requiredFields = currentStepElement.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (field.type === 'checkbox') {
                    if (!field.checked) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                } else if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            // Special validation for step 2
            if (currentStep === 2) {
                const provinceCheckboxes = currentStepElement.querySelectorAll('input[name="province_of_origin[]"]');
                const isProvinceSelected = Array.from(provinceCheckboxes).some(cb => cb.checked);
                
                if (!isProvinceSelected) {
                    document.getElementById('province_origin_error').style.display = 'block';
                    isValid = false;
                } else {
                    document.getElementById('province_origin_error').style.display = 'none';
                }
            }

            log(`✅ Validation result: ${isValid}`);
            return isValid;
        }

        // Next button handler
        function handleNext() {
            log('🚀 Next button clicked!');
            
            if (!validateStep()) {
                log('❌ Validation failed');
                return;
            }

            if (currentStep < totalSteps) {
                showStep(currentStep + 1);
            } else {
                log('⚠️ Already at last step');
            }
        }

        // Previous button handler
        function handlePrevious() {
            log('🔙 Previous button clicked!');
            
            if (currentStep > 1) {
                showStep(currentStep - 1);
            } else {
                log('⚠️ Already at first step');
            }
        }

        // Test all steps
        function testAllSteps() {
            log('🧪 Testing all steps automatically...');
            for (let i = 1; i <= totalSteps; i++) {
                setTimeout(() => {
                    log(`🎯 Auto-navigating to step ${i}`);
                    showStep(i);
                }, i * 1000);
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            log('🎬 DOM loaded, initializing...');

            // Get elements
            steps = document.querySelectorAll('.form-step');
            nextBtn = document.getElementById('nextBtn');
            prevBtn = document.getElementById('prevBtn');
            submitBtn = document.getElementById('submitBtn');
            progressBar = document.getElementById('progressBar');

            log(`📋 Found elements: steps=${steps.length}, nextBtn=${!!nextBtn}, prevBtn=${!!prevBtn}`);

            // Check if elements exist
            if (!nextBtn) {
                log('❌ Next button not found!');
                return;
            }

            if (steps.length === 0) {
                log('❌ No form steps found!');
                return;
            }

            // Add event listeners
            nextBtn.addEventListener('click', function(e) {
                e.preventDefault();
                log('🎯 Next button event fired!');
                handleNext();
            });

            if (prevBtn) {
                prevBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    log('🎯 Previous button event fired!');
                    handlePrevious();
                });
            }

            log('✅ Event listeners added');

            // Initialize first step
            showStep(1);

            // Start debug updates
            setInterval(updateDebug, 1000);

            log('🎉 Initialization complete!');
        });

        // Test if this script is running
        log('📜 Standalone script loaded successfully!');
    </script>
</body>
</html>
