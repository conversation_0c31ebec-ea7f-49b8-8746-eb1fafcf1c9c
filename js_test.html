<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test - Form Navigation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .form-step { 
            display: none; 
            border: 2px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .form-step.active { 
            display: block; 
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 300px;
            z-index: 1000;
        }
        .console-log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h6>🔍 Debug Panel</h6>
        <div id="debugInfo">Loading...</div>
        <div class="console-log" id="consoleLog">Console output will appear here...</div>
    </div>

    <div class="container py-4">
        <h1>🧪 JavaScript Navigation Test</h1>
        
        <div class="alert alert-info">
            <strong>Test Instructions:</strong>
            <ul class="mb-0">
                <li>Watch the debug panel on the right</li>
                <li>Check browser console (F12) for detailed logs</li>
                <li>Click Next to navigate through steps</li>
                <li>Fill required fields to proceed</li>
            </ul>
        </div>

        <!-- Progress Bar -->
        <div class="progress mb-4" style="height: 10px;">
            <div class="progress-bar bg-primary" role="progressbar" style="width: 20%" 
                 aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
        </div>

        <!-- Form -->
        <form id="scholarshipForm" class="card">
            <div class="card-body">
                <!-- Step 1 -->
                <div class="form-step active" data-step="1">
                    <h4 class="text-primary mb-3">
                        <i class="fas fa-user me-2"></i>Step 1: Basic Information
                    </h4>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="student_code" class="form-label">Student Code *</label>
                            <input type="text" class="form-control" id="student_code" name="student_code" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="form-step" data-step="2">
                    <h4 class="text-primary mb-3">
                        <i class="fas fa-map-marker-alt me-2"></i>Step 2: Location
                    </h4>
                    <div class="mb-3">
                        <label class="form-label">Province of Origin *</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Central" id="central">
                            <label class="form-check-label" for="central">Central</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Honiara" id="honiara">
                            <label class="form-check-label" for="honiara">Honiara</label>
                        </div>
                        <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                            Please select at least one province of origin.
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="form-step" data-step="3">
                    <h4 class="text-primary mb-3">
                        <i class="fas fa-plane me-2"></i>Step 3: Travel
                    </h4>
                    <div class="mb-3">
                        <label for="transport" class="form-label">Mode of Transport *</label>
                        <select class="form-select" id="transport" name="transport" required>
                            <option value="">Choose...</option>
                            <option value="Air">Air</option>
                            <option value="Sea">Sea</option>
                        </select>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="form-step" data-step="4">
                    <h4 class="text-primary mb-3">
                        <i class="fas fa-graduation-cap me-2"></i>Step 4: Academic
                    </h4>
                    <div class="mb-3">
                        <label for="qualifications" class="form-label">Academic Qualifications *</label>
                        <textarea class="form-control" id="qualifications" name="qualifications" required></textarea>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="form-step" data-step="5">
                    <h4 class="text-primary mb-3">
                        <i class="fas fa-file-signature me-2"></i>Step 5: Final
                    </h4>
                    <div class="mb-3">
                        <label for="motivation" class="form-label">Motivation Letter *</label>
                        <textarea class="form-control" id="motivation" name="motivation" required></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                        <label class="form-check-label" for="terms">I agree to the terms *</label>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                        <i class="fas fa-arrow-left me-2"></i>Previous
                    </button>
                    <button type="button" class="btn btn-primary" id="nextBtn">
                        Next<i class="fas fa-arrow-right ms-2"></i>
                    </button>
                    <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                        <i class="fas fa-paper-plane me-2"></i>Submit
                    </button>
                </div>
            </div>
        </form>

        <div class="mt-4">
            <h5>Manual Navigation Test</h5>
            <div class="btn-group">
                <button onclick="testShowStep(1)" class="btn btn-outline-primary btn-sm">Go to Step 1</button>
                <button onclick="testShowStep(2)" class="btn btn-outline-primary btn-sm">Go to Step 2</button>
                <button onclick="testShowStep(3)" class="btn btn-outline-primary btn-sm">Go to Step 3</button>
                <button onclick="testShowStep(4)" class="btn btn-outline-primary btn-sm">Go to Step 4</button>
                <button onclick="testShowStep(5)" class="btn btn-outline-primary btn-sm">Go to Step 5</button>
            </div>
        </div>
    </div>

    <!-- Include the fixed JavaScript -->
    <script src="assets/script.js"></script>
    
    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const logOutput = document.getElementById('consoleLog');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.join(' ');
            logOutput.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            logOutput.scrollTop = logOutput.scrollHeight;
        };
        
        // Debug info updater
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const form = window.scholarshipForm;
            
            if (form) {
                debugInfo.innerHTML = `
                    <small>
                    <strong>Current Step:</strong> ${form.currentStep}/${form.totalSteps}<br>
                    <strong>Steps Found:</strong> ${form.steps ? form.steps.length : 'N/A'}<br>
                    <strong>Active Step:</strong> ${document.querySelector('.form-step.active')?.dataset.step || 'None'}<br>
                    <strong>Next Btn:</strong> ${document.getElementById('nextBtn') ? '✅' : '❌'}<br>
                    <strong>Prev Btn:</strong> ${document.getElementById('prevBtn') ? '✅' : '❌'}<br>
                    <strong>Progress:</strong> ${form.progressBar?.style.width || 'Unknown'}
                    </small>
                `;
            } else {
                debugInfo.innerHTML = '<small style="color: red;">Form not initialized</small>';
            }
        }
        
        // Manual step navigation for testing
        function testShowStep(step) {
            console.log('Manual navigation to step:', step);
            if (typeof showStep === 'function') {
                showStep(step);
            } else {
                console.error('showStep function not available');
            }
        }
        
        // Update debug info every second
        setInterval(updateDebugInfo, 1000);
        
        // Initial debug update
        setTimeout(updateDebugInfo, 500);
    </script>
</body>
</html>
