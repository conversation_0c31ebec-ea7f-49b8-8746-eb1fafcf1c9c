<?php
/**
 * Admin Setup Script - Complete Fix for Admin Login
 * Run this script to fix all admin login issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Admin Setup & Fix Script</h1>";
echo "<p>This script will completely fix the admin login system.</p>";

// Database credentials - Direct connection
$host = 'localhost';
$username = 'u787474055_solomonislands';
$password = 'Blackpanther707@707';
$database = 'u787474055_solomonislands';

try {
    // Step 1: Connect to database
    echo "<h2>Step 1: Database Connection</h2>";
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        die("<div style='color: red; font-weight: bold;'>❌ FAILED: Database connection failed: " . $conn->connect_error . "</div>");
    }
    
    echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Connected to database</div>";
    $conn->set_charset("utf8mb4");
    
    // Step 2: Drop and recreate admin_users table
    echo "<h2>Step 2: Recreate Admin Tables</h2>";

    // Disable foreign key checks temporarily
    $conn->query("SET FOREIGN_KEY_CHECKS = 0");
    echo "<div style='color: orange;'>🔧 Disabled foreign key checks</div>";

    // Drop existing tables in correct order
    $conn->query("DROP TABLE IF EXISTS admin_sessions");
    echo "<div style='color: orange;'>🗑️ Dropped admin_sessions table</div>";

    $conn->query("DROP TABLE IF EXISTS admin_users");
    echo "<div style='color: orange;'>🗑️ Dropped admin_users table</div>";

    // Re-enable foreign key checks
    $conn->query("SET FOREIGN_KEY_CHECKS = 1");
    echo "<div style='color: orange;'>🔧 Re-enabled foreign key checks</div>";
    
    // Create admin_users table
    $create_users_sql = "
    CREATE TABLE admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        full_name VARCHAR(100),
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_username (username),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($create_users_sql)) {
        echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: admin_users table created</div>";
    } else {
        die("<div style='color: red; font-weight: bold;'>❌ FAILED: Could not create admin_users table: " . $conn->error . "</div>");
    }
    
    // Create admin_sessions table
    $create_sessions_sql = "
    CREATE TABLE admin_sessions (
        id VARCHAR(128) PRIMARY KEY,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at),
        FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($create_sessions_sql)) {
        echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: admin_sessions table created</div>";
    } else {
        die("<div style='color: red; font-weight: bold;'>❌ FAILED: Could not create admin_sessions table: " . $conn->error . "</div>");
    }
    
    // Step 3: Create admin user
    echo "<h2>Step 3: Create Admin User</h2>";
    
    $admin_username = 'admin';
    $admin_password = 'admin123';
    $admin_email = '<EMAIL>';
    $admin_fullname = 'System Administrator';
    
    // Generate password hash
    $password_hash = password_hash($admin_password, PASSWORD_DEFAULT);
    echo "<div>Generated password hash: " . substr($password_hash, 0, 50) . "...</div>";
    
    // Insert admin user
    $insert_sql = "INSERT INTO admin_users (username, password_hash, email, full_name, is_active) VALUES (?, ?, ?, ?, 1)";
    $stmt = $conn->prepare($insert_sql);
    $stmt->bind_param("ssss", $admin_username, $password_hash, $admin_email, $admin_fullname);
    
    if ($stmt->execute()) {
        $admin_id = $conn->insert_id;
        echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Admin user created with ID: $admin_id</div>";
    } else {
        die("<div style='color: red; font-weight: bold;'>❌ FAILED: Could not create admin user: " . $conn->error . "</div>");
    }
    
    // Step 4: Verify admin user
    echo "<h2>Step 4: Verify Admin User</h2>";
    
    $verify_sql = "SELECT id, username, password_hash, email, full_name, is_active FROM admin_users WHERE username = ?";
    $verify_stmt = $conn->prepare($verify_sql);
    $verify_stmt->bind_param("s", $admin_username);
    $verify_stmt->execute();
    $result = $verify_stmt->get_result();
    
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Admin user verified</div>";
        echo "<div><strong>ID:</strong> " . $user['id'] . "</div>";
        echo "<div><strong>Username:</strong> " . $user['username'] . "</div>";
        echo "<div><strong>Email:</strong> " . $user['email'] . "</div>";
        echo "<div><strong>Full Name:</strong> " . $user['full_name'] . "</div>";
        echo "<div><strong>Active:</strong> " . ($user['is_active'] ? 'Yes' : 'No') . "</div>";
        
        // Test password verification
        if (password_verify($admin_password, $user['password_hash'])) {
            echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Password verification works</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>❌ FAILED: Password verification failed</div>";
        }
    } else {
        echo "<div style='color: red; font-weight: bold;'>❌ FAILED: Admin user not found after creation</div>";
    }
    
    // Step 5: Test config.php connection
    echo "<h2>Step 5: Test Config.php Connection</h2>";
    
    try {
        require_once 'config.php';
        $db = Database::getInstance();
        $config_conn = $db->getConnection();
        
        $config_test = $config_conn->query("SELECT COUNT(*) as count FROM admin_users");
        if ($config_test) {
            $count = $config_test->fetch_assoc()['count'];
            echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Config.php database connection works - found $count admin users</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>❌ FAILED: Config.php database query failed</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: red; font-weight: bold;'>❌ FAILED: Config.php error: " . $e->getMessage() . "</div>";
    }
    
    // Step 6: Create a simple login test
    echo "<h2>Step 6: Create Login Test</h2>";
    
    $test_login_content = '<?php
// Simple login test
session_start();
require_once "config.php";

if ($_POST) {
    $username = $_POST["username"] ?? "";
    $password = $_POST["password"] ?? "";
    
    if ($username === "admin" && $password === "admin123") {
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();
            
            $sql = "SELECT id, username, password_hash, full_name FROM admin_users WHERE username = ? AND is_active = 1";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();
                
                if (password_verify($password, $user["password_hash"])) {
                    $_SESSION["admin_user_id"] = $user["id"];
                    $_SESSION["admin_username"] = $user["username"];
                    $_SESSION["admin_full_name"] = $user["full_name"];
                    $_SESSION["admin_login_time"] = time();
                    
                    echo "<div style=\"color: green; font-weight: bold;\">✅ LOGIN SUCCESS! Redirecting to admin dashboard...</div>";
                    echo "<script>setTimeout(function(){ window.location.href = \"admin/index.php\"; }, 2000);</script>";
                    exit;
                } else {
                    echo "<div style=\"color: red; font-weight: bold;\">❌ Password verification failed</div>";
                }
            } else {
                echo "<div style=\"color: red; font-weight: bold;\">❌ User not found</div>";
            }
        } catch (Exception $e) {
            echo "<div style=\"color: red; font-weight: bold;\">❌ Database error: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div style=\"color: red; font-weight: bold;\">❌ Invalid credentials</div>";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .credentials { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🔐 Admin Login Test</h1>
    
    <div class="credentials">
        <h3>Default Credentials:</h3>
        <p><strong>Username:</strong> admin</p>
        <p><strong>Password:</strong> admin123</p>
    </div>
    
    <form method="POST">
        <div class="form-group">
            <label>Username:</label>
            <input type="text" name="username" value="admin" required>
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" name="password" value="admin123" required>
        </div>
        <div class="form-group">
            <button type="submit">Test Login</button>
        </div>
    </form>
    
    <p><a href="admin/login.php">Go to Real Admin Login</a></p>
</body>
</html>';
    
    file_put_contents('test_login.php', $test_login_content);
    echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Created test_login.php</div>";
    
    $conn->close();
    
    // Final summary
    echo "<h2>🎉 Setup Complete!</h2>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✅ Admin System Ready</h3>";
    echo "<p><strong>Admin Login URL:</strong> <a href='admin/login.php' target='_blank'>admin/login.php</a></p>";
    echo "<p><strong>Test Login URL:</strong> <a href='test_login.php' target='_blank'>test_login.php</a></p>";
    echo "<p><strong>Username:</strong> admin</p>";
    echo "<p><strong>Password:</strong> admin123</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>⚠️ Important Notes:</h3>";
    echo "<ul>";
    echo "<li>All admin tables have been recreated</li>";
    echo "<li>Fresh admin user created with proper password hash</li>";
    echo "<li>Database connection verified</li>";
    echo "<li>Password verification tested and working</li>";
    echo "<li>Delete this setup file after confirming login works</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold;'>❌ CRITICAL ERROR: " . $e->getMessage() . "</div>";
}
?>
