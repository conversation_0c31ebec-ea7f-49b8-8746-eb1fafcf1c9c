<?php
require_once 'config.php';

// Generate CSRF token
$csrf_token = generateCSRFToken();

// Get any error or success messages
$error_message = getErrorMessage();
$success_message = getSuccessMessage();

// Check if we need to show terms error and restore form data
$show_terms_error = isset($_SESSION['show_terms_error']) && $_SESSION['show_terms_error'];
$form_data = isset($_SESSION['form_data']) ? $_SESSION['form_data'] : [];

// Clear the session data after retrieving it
if ($show_terms_error) {
    unset($_SESSION['show_terms_error']);
    unset($_SESSION['form_data']);
}

// Helper function to get form value with preserved data
function getFormValue($field_name, $default = '') {
    global $form_data;
    return isset($form_data[$field_name]) ? htmlspecialchars($form_data[$field_name]) : $default;
}

// Helper function to check if checkbox should be checked
function isChecked($field_name, $value = '1') {
    global $form_data;
    if (isset($form_data[$field_name])) {
        if (is_array($form_data[$field_name])) {
            return in_array($value, $form_data[$field_name]);
        }
        return $form_data[$field_name] == $value;
    }
    return false;
}

// Helper function to check if option should be selected
function isSelected($field_name, $value) {
    global $form_data;
    if (isset($form_data[$field_name])) {
        if (is_array($form_data[$field_name])) {
            return in_array($value, $form_data[$field_name]);
        }
        return $form_data[$field_name] == $value;
    }
    return false;
}

// Province options
$provinces = [
    'Central', 'Choiseul', 'Guadalcanal', 'Isabel', 'Makira-Ulawa', 
    'Malaita', 'Rennell and Bellona', 'Temotu', 'Western', 'Honiara'
];

// HEI options
$hei_options = [
    'University of Papua New Guinea (UPNG)', 
    'University of Technology (UOT)', 
    'Papua New Guinea University of Natural Resources and Environment (PNGUNRE)',
    'Divine Word University (DWU)',
    'Pacific Adventist University (PAU)',
    'University of Goroka (UOG)'
];

// PNG Province options
$png_provinces = [
    'National Capital District (NCD)', 'Morobe', 'Western Highlands', 'Eastern Highlands',
    'Madang', 'East New Britain', 'West New Britain', 'Manus', 'New Ireland',
    'Bougainville', 'Gulf', 'Central', 'Milne Bay', 'Oro', 'Southern Highlands',
    'Hela', 'Jiwaka', 'Chimbu', 'Western', 'Enga'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Page Header -->
    <section class="bg-primary text-white py-4" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="h2 fw-bold mb-2">
                        <i class="fas fa-user-plus me-2"></i>
                        Student Registration
                    </h1>
                    <p class="mb-0">
                        Complete your registration for the PNG Higher Education Program
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container my-5">
        <!-- Alert Messages -->
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>



        <!-- Application Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h3 class="card-title mb-0 h5">
                            Registration Form
                        </h3>
                        <small class="text-muted">Fields marked with * are required</small>
                    </div>
                    
                    <div class="card-body">
                        <form id="scholarshipForm" action="submit_fixed.php" method="POST" novalidate>
                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            
                            <!-- Progress Bar -->
                            <div class="progress mb-4" style="height: 6px;">
                                <div class="progress-bar" role="progressbar" style="width: 33%" aria-valuenow="33" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>

                            <!-- Step 1: Personal Information -->
                            <div class="form-step active" data-step="1">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>
                                    Personal Information
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="student_code" class="form-label">
                                            Student Code <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="student_code" name="student_code"
                                               required maxlength="20" placeholder="Your assigned student code"
                                               value="<?php echo getFormValue('student_code'); ?>">
                                        <div class="invalid-feedback">Please provide a valid student code.</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">
                                            Full Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="full_name" name="full_name"
                                               required maxlength="100" placeholder="Your full name"
                                               value="<?php echo getFormValue('full_name'); ?>">
                                        <div class="invalid-feedback">Please provide your full name.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                                        <input type="date" class="form-control" id="date_of_birth" name="date_of_birth"
                                               value="<?php echo getFormValue('date_of_birth'); ?>">
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="gender" class="form-label">Gender</label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="">Choose...</option>
                                            <option value="Male" <?php echo isSelected('gender', 'Male') ? 'selected' : ''; ?>>Male</option>
                                            <option value="Female" <?php echo isSelected('gender', 'Female') ? 'selected' : ''; ?>>Female</option>
                                            <option value="Other" <?php echo isSelected('gender', 'Other') ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               maxlength="20" placeholder="+677 XXXXXXX"
                                               value="<?php echo getFormValue('phone'); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               maxlength="100" placeholder="<EMAIL>"
                                               value="<?php echo getFormValue('email'); ?>">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Province of Origin <span class="text-danger">*</span></label>
                                        <select class="form-select" name="province_of_origin[]" required>
                                            <option value="">Choose your province...</option>
                                            <?php foreach($provinces as $province): ?>
                                            <option value="<?php echo $province; ?>" <?php echo isSelected('province_of_origin', $province) ? 'selected' : ''; ?>><?php echo $province; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select your province of origin.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Current Province of Residence</label>
                                        <select class="form-select" name="current_residential_province[]">
                                            <option value="">Choose your current province...</option>
                                            <?php foreach($provinces as $province): ?>
                                            <option value="<?php echo $province; ?>" <?php echo isSelected('current_residential_province', $province) ? 'selected' : ''; ?>><?php echo $province; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Hidden field to set all students as Selected -->
                                <input type="hidden" name="selection_status" value="Selected">
                            </div>

                            <!-- Step 2: Academic & Institution Information -->
                            <div class="form-step" data-step="2">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-graduation-cap me-2"></i>
                                    Academic & Institution Information
                                </h5>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="hei_name" class="form-label">
                                            Assigned Institution <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="hei_name" name="hei_name" required>
                                            <option value="">Choose your assigned institution...</option>
                                            <?php foreach($hei_options as $hei): ?>
                                            <option value="<?php echo $hei; ?>" <?php echo isSelected('hei_name', $hei) ? 'selected' : ''; ?>><?php echo $hei; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select your assigned institution.</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="course_of_study" class="form-label">
                                            Course of Study <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="course_of_study"
                                               name="course_of_study" required maxlength="200"
                                               placeholder="e.g., Bachelor of Engineering"
                                               value="<?php echo getFormValue('course_of_study'); ?>">
                                        <div class="invalid-feedback">Please specify your course of study.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label for="academic_qualifications" class="form-label">
                                            Academic Qualifications <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control" id="academic_qualifications"
                                                  name="academic_qualifications" rows="3" required maxlength="1000"
                                                  placeholder="List your academic qualifications, certificates, diplomas, degrees..."><?php echo getFormValue('academic_qualifications'); ?></textarea>
                                        <div class="invalid-feedback">Please provide your academic qualifications.</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Travel & Final Details -->
                            <div class="form-step" data-step="3">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-plane me-2"></i>
                                    Travel & Final Details
                                </h5>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="mode_of_transport_to_honiara" class="form-label">
                                            Transport to Honiara <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="mode_of_transport_to_honiara"
                                                name="mode_of_transport_to_honiara" required>
                                            <option value="">Choose...</option>
                                            <option value="Air" <?php echo isSelected('mode_of_transport_to_honiara', 'Air') ? 'selected' : ''; ?>>Air</option>
                                            <option value="Sea" <?php echo isSelected('mode_of_transport_to_honiara', 'Sea') ? 'selected' : ''; ?>>Sea</option>
                                            <option value="Land" <?php echo isSelected('mode_of_transport_to_honiara', 'Land') ? 'selected' : ''; ?>>Land</option>
                                            <option value="Other" <?php echo isSelected('mode_of_transport_to_honiara', 'Other') ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                        <div class="invalid-feedback">Please select a mode of transport.</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="png_province" class="form-label">
                                            PNG Province <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="png_province" name="png_province" required>
                                            <option value="">Choose...</option>
                                            <?php foreach($png_provinces as $province): ?>
                                            <option value="<?php echo $province; ?>" <?php echo isSelected('png_province', $province) ? 'selected' : ''; ?>><?php echo $province; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select a PNG province.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label for="motivation_letter" class="form-label">
                                            Personal Statement <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control" id="motivation_letter"
                                                  name="motivation_letter" rows="4" required maxlength="1000"
                                                  placeholder="Write about your academic background, career goals, and how you plan to contribute to Solomon Islands after your studies..."><?php echo getFormValue('motivation_letter'); ?></textarea>
                                        <div class="invalid-feedback">Please provide a personal statement.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-4">
                                        <div class="card border-warning bg-light" id="terms-section">
                                            <div class="card-body">
                                                <h6 class="card-title text-warning mb-3">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    Important: Terms and Conditions
                                                </h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="terms_agreement"
                                                           name="terms_agreement" value="1" required
                                                           <?php echo isChecked('terms_agreement', '1') ? 'checked' : ''; ?>>
                                                    <label class="form-check-label fw-bold" for="terms_agreement">
                                                        I confirm that all information provided is accurate and complete.
                                                        I understand that providing false information may affect my travel arrangements and scholarship eligibility.
                                                        <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="invalid-feedback">You must agree to the terms before submitting.</div>
                                                </div>
                                                <small class="text-muted mt-2 d-block">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    By checking this box, you acknowledge that you have read and agree to the terms and conditions of the PNG Higher Education Program.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hidden fields for removed fields -->
                                <input type="hidden" name="honiara_pom_route" value="">
                                <input type="hidden" name="travel_question" value="">
                                <input type="hidden" name="estimated_cost" value="0">
                            </div>

                            <!-- Navigation Buttons -->
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-outline-secondary" id="prevBtn" style="display: none;">
                                    <i class="fas fa-arrow-left me-2"></i>Previous
                                </button>
                                <button type="button" class="btn btn-primary" id="nextBtn">
                                    Next<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                                    <i class="fas fa-check me-2"></i>Submit Registration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Custom CSS for Mobile -->
    <style>
        /* Mobile Responsive Form */
        @media (max-width: 768px) {
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }

            .card {
                margin: 0 5px;
            }

            .form-step h5 {
                font-size: 1.1rem;
            }

            .btn {
                font-size: 0.9rem;
            }

            .progress {
                height: 4px !important;
            }
        }

        @media (max-width: 576px) {
            .col-md-6, .col-md-4 {
                margin-bottom: 1rem;
            }

            .d-flex.justify-content-between {
                flex-direction: column;
                gap: 10px;
            }

            .d-flex.justify-content-between button {
                width: 100%;
            }
        }

        /* Form styling improvements */
        .form-step {
            min-height: 400px;
            display: none;
        }

        .form-step.active {
            display: block;
        }

        .form-control, .form-select {
            border-radius: 8px;
        }

        .btn {
            border-radius: 8px;
        }

        .card {
            border-radius: 12px;
        }

        /* Terms and conditions styling */
        #terms_agreement {
            transform: scale(1.2);
            margin-right: 10px;
        }

        .form-check-label {
            cursor: pointer;
        }

        /* Highlight animation for terms section */
        .terms-highlight {
            animation: highlightPulse 2s ease-in-out;
        }

        @keyframes highlightPulse {
            0% { background-color: #fff3cd; }
            50% { background-color: #ffeaa7; }
            100% { background-color: #fff3cd; }
        }

        /* Error message styling */
        #terms-error-message {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS - Fixed Navigation Script -->
    <script src="assets/form-nav-fixed.js?v=<?php echo time(); ?>"></script>

    <?php if ($show_terms_error): ?>
    <script>
        // Handle terms error - navigate to step 3 and highlight terms section
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for the form navigation to initialize
            setTimeout(function() {
                // Navigate to step 3 (where terms are)
                if (typeof showStep === 'function') {
                    showStep(3);
                } else if (window.displayStep) {
                    window.displayStep(3);
                }

                // Show error message and highlight terms section
                setTimeout(function() {
                    var termsSection = document.getElementById('terms-section');
                    var termsCheckbox = document.getElementById('terms_agreement');

                    if (termsSection && termsCheckbox) {
                        // Add error styling to checkbox
                        termsCheckbox.classList.add('is-invalid');

                        // Highlight the terms section
                        termsSection.style.backgroundColor = '#fff3cd';
                        termsSection.style.border = '2px solid #dc3545';
                        termsSection.style.borderRadius = '8px';
                        termsSection.style.transition = 'all 0.3s ease';

                        // Scroll to terms section
                        termsSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });

                        // Focus on checkbox
                        setTimeout(function() {
                            termsCheckbox.focus();
                        }, 500);

                        // Remove highlight after 5 seconds
                        setTimeout(function() {
                            termsSection.style.backgroundColor = '';
                            termsSection.style.border = '';
                        }, 5000);
                    }
                }, 500);
            }, 100);
        });
    </script>
    <?php endif; ?>
</body>
</html>
