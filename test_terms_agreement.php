<?php
/**
 * Test Terms Agreement Checkbox
 * Quick test to verify the terms agreement is working
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Terms Agreement Test</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>POST Data Received:</h2>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h2>Terms Agreement Check:</h2>";
    
    if (isset($_POST['terms_agreement'])) {
        echo "<div style='color: green; font-weight: bold;'>✅ terms_agreement field is present</div>";
        echo "<div>Value: '" . $_POST['terms_agreement'] . "'</div>";
        
        if ($_POST['terms_agreement'] === '1') {
            echo "<div style='color: green; font-weight: bold;'>✅ Terms agreement value is correct ('1')</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>❌ Terms agreement value is incorrect (expected '1', got '" . $_POST['terms_agreement'] . "')</div>";
        }
    } else {
        echo "<div style='color: red; font-weight: bold;'>❌ terms_agreement field is missing from POST data</div>";
    }
    
    // Test the exact validation logic from submit_fixed.php
    echo "<h2>Validation Logic Test:</h2>";
    if (!isset($_POST['terms_agreement']) || $_POST['terms_agreement'] !== '1') {
        echo "<div style='color: red; font-weight: bold;'>❌ Validation FAILED: You must agree to the terms and conditions</div>";
    } else {
        echo "<div style='color: green; font-weight: bold;'>✅ Validation PASSED: Terms agreement is valid</div>";
    }
    
} else {
    echo "<p>This page will test the terms agreement checkbox submission.</p>";
}

echo "<h2>Test Form</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 10px 0;'>";
echo "<form method='POST' action='test_terms_agreement.php'>";

echo "<h4>Sample Form Fields:</h4>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Student Code:</label><br>";
echo "<input type='text' name='student_code' value='TEST001' required style='width: 200px; padding: 5px;'>";
echo "</div>";

echo "<div style='margin: 10px 0;'>";
echo "<label>Full Name:</label><br>";
echo "<input type='text' name='full_name' value='Test Student' required style='width: 200px; padding: 5px;'>";
echo "</div>";

echo "<h4>Terms Agreement (This is what we're testing):</h4>";
echo "<div style='margin: 10px 0; padding: 15px; border: 2px solid #007bff; border-radius: 5px; background: #e7f3ff;'>";
echo "<div class='form-check'>";
echo "<input type='checkbox' id='terms_agreement' name='terms_agreement' value='1' required>";
echo "<label for='terms_agreement' style='margin-left: 10px;'>";
echo "I confirm that all information provided is accurate and complete. ";
echo "I understand that providing false information may result in disqualification. <span style='color: red;'>*</span>";
echo "</label>";
echo "</div>";
echo "</div>";

echo "<div style='margin: 20px 0;'>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Submit</button>";
echo "</div>";

echo "</form>";
echo "</div>";

echo "<h2>📋 What This Test Shows</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<ul>";
echo "<li><strong>POST Data:</strong> Shows all form data received</li>";
echo "<li><strong>Field Presence:</strong> Checks if terms_agreement field exists</li>";
echo "<li><strong>Field Value:</strong> Verifies the value is '1' when checked</li>";
echo "<li><strong>Validation Logic:</strong> Tests the exact same validation used in submit_fixed.php</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔧 Fix Applied</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>✅ Checkbox Fix:</h4>";
echo "<p>Added <code>value=\"1\"</code> attribute to the terms agreement checkbox in application.php</p>";
echo "<p><strong>Before:</strong> <code>&lt;input type=\"checkbox\" name=\"terms_agreement\" required&gt;</code></p>";
echo "<p><strong>After:</strong> <code>&lt;input type=\"checkbox\" name=\"terms_agreement\" value=\"1\" required&gt;</code></p>";
echo "</div>";

echo "<h2>🎯 Next Steps</h2>";
echo "<div style='background: #cff4fc; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<ol>";
echo "<li><strong>Test this form above</strong> - Check the checkbox and submit</li>";
echo "<li><strong>Verify it shows validation PASSED</strong></li>";
echo "<li><strong>Test the main application form</strong> - <a href='application.php' target='_blank'>application.php</a></li>";
echo "<li><strong>Complete all 5 steps and submit</strong></li>";
echo "</ol>";
echo "</div>";
?>
