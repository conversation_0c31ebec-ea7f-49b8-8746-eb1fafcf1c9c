<?php
/**
 * API endpoint to check registration status
 * This file handles AJAX requests from status.php
 */

require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['reference_number']) || !isset($input['student_code'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required fields']);
    exit;
}

$reference_number = trim($input['reference_number']);
$student_code = trim($input['student_code']);

// Validate reference number format (SI-YYYY-XXXXXX)
if (!preg_match('/^SI-\d{4}-\d{6}$/', $reference_number)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid reference number format']);
    exit;
}

try {
    // Get database connection
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Extract application ID from reference number
    $ref_parts = explode('-', $reference_number);
    $year = $ref_parts[1];
    $app_id = intval($ref_parts[2]);
    
    // Query to find the registration
    $sql = "SELECT id, student_code, full_name, selection_status, created_at, updated_at 
            FROM applications 
            WHERE id = ? AND student_code = ? AND YEAR(created_at) = ?";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Database prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("iss", $app_id, $student_code, $year);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['error' => 'Registration not found. Please check your reference number and student code.']);
        exit;
    }
    
    $registration = $result->fetch_assoc();
    
    // Format the response
    $response = [
        'success' => true,
        'data' => [
            'reference' => $reference_number,
            'name' => $registration['full_name'],
            'student_code' => $registration['student_code'],
            'status' => $registration['selection_status'],
            'submitted_date' => date('F j, Y', strtotime($registration['created_at'])),
            'last_updated' => date('F j, Y \a\t g:i A', strtotime($registration['updated_at'])),
            'registration_id' => $registration['id']
        ]
    ];
    
    // Log the status check
    logActivity("Status check performed for registration ID: {$registration['id']}, Student: {$registration['full_name']}", 'INFO');
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Log the error
    logActivity("Status check failed: " . $e->getMessage(), 'ERROR');
    
    http_response_code(500);
    echo json_encode(['error' => 'An error occurred while checking status. Please try again later.']);
}
?>
