<?php
/**
 * Debug version of status check API
 */

require_once '../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Method not allowed', 'method' => $_SERVER['REQUEST_METHOD']]);
    exit;
}

// Get JSON input
$input_raw = file_get_contents('php://input');
$input = json_decode($input_raw, true);

// Debug information
$debug_info = [
    'raw_input' => $input_raw,
    'parsed_input' => $input,
    'json_error' => json_last_error_msg()
];

// Validate input
if (!isset($input['reference_number']) || !isset($input['student_code'])) {
    echo json_encode([
        'error' => 'Missing required fields',
        'debug' => $debug_info,
        'received_fields' => array_keys($input ?: [])
    ]);
    exit;
}

$reference_number = trim($input['reference_number']);
$student_code = trim($input['student_code']);

// Validate reference number format (SI-YYYY-XXXXXX)
if (!preg_match('/^SI-\d{4}-\d{6}$/', $reference_number)) {
    echo json_encode([
        'error' => 'Invalid reference number format',
        'reference_number' => $reference_number,
        'expected_format' => 'SI-YYYY-XXXXXX',
        'debug' => $debug_info
    ]);
    exit;
}

try {
    // Get database connection
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Extract application ID from reference number
    $ref_parts = explode('-', $reference_number);
    $year = $ref_parts[1];
    $app_id = intval($ref_parts[2]);
    
    // Debug query info
    $query_debug = [
        'app_id' => $app_id,
        'student_code' => $student_code,
        'year' => $year
    ];
    
    // Query to find the registration
    $sql = "SELECT id, student_code, full_name, selection_status, created_at, updated_at 
            FROM applications 
            WHERE id = ? AND student_code = ? AND YEAR(created_at) = ?";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Database prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("iss", $app_id, $student_code, $year);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // Check if record exists with different criteria
        $check_sql = "SELECT id, student_code, full_name, YEAR(created_at) as year FROM applications WHERE id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("i", $app_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        $existing_record = $check_result->fetch_assoc();
        
        echo json_encode([
            'error' => 'Registration not found',
            'debug' => $debug_info,
            'query_debug' => $query_debug,
            'existing_record' => $existing_record,
            'message' => 'Please check your reference number and student code.'
        ]);
        exit;
    }
    
    $registration = $result->fetch_assoc();
    
    // Format the response
    $response = [
        'success' => true,
        'data' => [
            'reference' => $reference_number,
            'name' => $registration['full_name'],
            'student_code' => $registration['student_code'],
            'status' => $registration['selection_status'],
            'submitted_date' => date('F j, Y', strtotime($registration['created_at'])),
            'last_updated' => date('F j, Y \a\t g:i A', strtotime($registration['updated_at'])),
            'registration_id' => $registration['id']
        ],
        'debug' => $debug_info,
        'query_debug' => $query_debug
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'Database error: ' . $e->getMessage(),
        'debug' => $debug_info
    ]);
}
?>
