<?php
/**
 * Test Admin Delete Functionality
 * Verify the delete application feature is working
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🗑️ Admin Delete Functionality Test</h1>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>✅ Delete Application Feature Added!</h2>";
echo "<p><strong>Admin Dashboard now has delete functionality for applications</strong></p>";
echo "</div>";

// Check if admin files exist
echo "<h2>📁 File Check</h2>";
$admin_files = [
    'admin/delete_application.php' => 'Delete handler with confirmation page',
    'admin/index.php' => 'Updated dashboard with delete button'
];

foreach ($admin_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div style='color: green;'>✅ $file - $description</div>";
    } else {
        echo "<div style='color: red;'>❌ $file - $description (MISSING)</div>";
    }
}

// Check database connection and applications
echo "<h2>🗄️ Database Status</h2>";
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<div style='color: green;'>✅ Database connection successful</div>";
    
    // Count applications
    $count_result = $conn->query("SELECT COUNT(*) as count FROM applications");
    $count = $count_result->fetch_assoc()['count'];
    echo "<div style='color: blue;'>📊 Current applications in database: $count</div>";
    
    if ($count > 0) {
        // Show recent applications
        echo "<h3>Recent Applications (for testing delete):</h3>";
        $recent = $conn->query("SELECT id, student_code, full_name, selection_status, created_at FROM applications ORDER BY created_at DESC LIMIT 5");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Student Code</th><th>Full Name</th><th>Status</th><th>Created</th><th>Test Delete</th></tr>";
        while ($row = $recent->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['student_code']}</td>";
            echo "<td>{$row['full_name']}</td>";
            echo "<td>{$row['selection_status']}</td>";
            echo "<td>" . date('M j, Y g:i A', strtotime($row['created_at'])) . "</td>";
            echo "<td><a href='admin/delete_application.php?id={$row['id']}' target='_blank' style='color: red;'>Delete</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ No Applications to Test Delete</h4>";
        echo "<p>You need to have some applications in the database to test the delete functionality.</p>";
        echo "<p><a href='application.php' target='_blank'>Submit a test application</a> first.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Database error: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 How to Test Delete Functionality</h2>";
echo "<div style='background: #cff4fc; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Step-by-Step Testing:</h4>";
echo "<ol>";
echo "<li><strong>Login to Admin Panel:</strong> <a href='admin/login.php' target='_blank'>admin/login.php</a></li>";
echo "<li><strong>Go to Dashboard:</strong> <a href='admin/index.php' target='_blank'>admin/index.php</a></li>";
echo "<li><strong>Find an application</strong> in the table</li>";
echo "<li><strong>Click the red trash icon</strong> in the Actions column</li>";
echo "<li><strong>Confirm deletion</strong> in the popup dialog</li>";
echo "<li><strong>Review confirmation page</strong> with application details</li>";
echo "<li><strong>Click 'Delete Application'</strong> to permanently delete</li>";
echo "<li><strong>Verify success message</strong> appears on dashboard</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔧 Features Added</h2>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>✅ Delete Button in Admin Dashboard:</h4>";
echo "<ul>";
echo "<li>Red trash icon in Actions column</li>";
echo "<li>Tooltip shows 'Delete Application'</li>";
echo "<li>JavaScript confirmation dialog</li>";
echo "<li>Passes application ID and student name</li>";
echo "</ul>";

echo "<h4>✅ Delete Confirmation Page:</h4>";
echo "<ul>";
echo "<li>Shows complete application details</li>";
echo "<li>Warning about permanent deletion</li>";
echo "<li>Security notice about logging</li>";
echo "<li>CSRF token protection</li>";
echo "<li>Option to view details or cancel</li>";
echo "</ul>";

echo "<h4>✅ Security Features:</h4>";
echo "<ul>";
echo "<li>Admin login required</li>";
echo "<li>CSRF token validation</li>";
echo "<li>Double confirmation (popup + page)</li>";
echo "<li>Activity logging for audit trail</li>";
echo "<li>Success/error message feedback</li>";
echo "</ul>";
echo "</div>";

echo "<h2>⚠️ Security Considerations</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Important Notes:</h4>";
echo "<ul>";
echo "<li><strong>Permanent Action:</strong> Deleted applications cannot be recovered</li>";
echo "<li><strong>Audit Trail:</strong> All deletions are logged with admin username</li>";
echo "<li><strong>Alternative:</strong> Consider changing status to 'Rejected' instead of deletion</li>";
echo "<li><strong>Backup:</strong> Ensure regular database backups before using delete feature</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🎉 Ready to Use!</h2>";
echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>";
echo "<h3 style='color: #0c5460;'>Admin Delete Functionality is Now Available!</h3>";
echo "<p><strong>Admins can now delete applications directly from the dashboard</strong></p>";
echo "<a href='admin/index.php' class='btn btn-primary btn-lg' style='text-decoration: none; background: #007bff; color: white; padding: 15px 30px; border-radius: 8px; display: inline-block; margin: 10px;'>🚀 Go to Admin Dashboard</a>";
echo "</div>";

// Create a test application for demonstration
echo "<h2>🧪 Create Test Application</h2>";
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>If you need a test application to practice deleting:</p>";
echo "<form method='POST' action='submit_fixed.php' style='display: inline;'>";
echo "<input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>";
echo "<input type='hidden' name='student_code' value='DELETE_TEST_" . time() . "'>";
echo "<input type='hidden' name='full_name' value='Test Delete User'>";
echo "<input type='hidden' name='province_of_origin[]' value='Central'>";
echo "<input type='hidden' name='mode_of_transport_to_honiara' value='Air'>";
echo "<input type='hidden' name='hei_name' value='University of Papua New Guinea (UPNG)'>";
echo "<input type='hidden' name='png_province' value='National Capital District (NCD)'>";
echo "<input type='hidden' name='academic_qualifications' value='Test qualifications for deletion'>";
echo "<input type='hidden' name='motivation_letter' value='Test motivation letter for deletion'>";
echo "<input type='hidden' name='terms_agreement' value='1'>";
echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Create Test Application</button>";
echo "</form>";
echo "<small style='color: #6c757d; margin-left: 10px;'>This will create a test application that you can safely delete</small>";
echo "</div>";
?>
