<?php
/**
 * Simple Admin Login - Backup login system
 * Use this if the main admin login is not working
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database credentials
$host = 'localhost';
$username = 'u787474055_solomonislands';
$password = 'Blackpanther707@707';
$database = 'u787474055_solomonislands';

$error_message = '';
$success_message = '';

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $login_username = trim($_POST['username'] ?? '');
    $login_password = $_POST['password'] ?? '';
    
    if (empty($login_username) || empty($login_password)) {
        $error_message = 'Please enter both username and password.';
    } else {
        try {
            // Direct database connection
            $conn = new mysqli($host, $username, $password, $database);
            
            if ($conn->connect_error) {
                $error_message = 'Database connection failed: ' . $conn->connect_error;
            } else {
                // Query admin user
                $sql = "SELECT id, username, password_hash, full_name FROM admin_users WHERE username = ? AND is_active = 1";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("s", $login_username);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows === 1) {
                    $user = $result->fetch_assoc();
                    
                    if (password_verify($login_password, $user['password_hash'])) {
                        // Login successful
                        $_SESSION['admin_user_id'] = $user['id'];
                        $_SESSION['admin_username'] = $user['username'];
                        $_SESSION['admin_full_name'] = $user['full_name'];
                        $_SESSION['admin_login_time'] = time();
                        
                        // Update last login
                        $update_sql = "UPDATE admin_users SET last_login = NOW() WHERE id = ?";
                        $update_stmt = $conn->prepare($update_sql);
                        $update_stmt->bind_param("i", $user['id']);
                        $update_stmt->execute();
                        
                        $success_message = 'Login successful! Redirecting to admin dashboard...';
                        
                        // Redirect after 2 seconds
                        echo "<script>setTimeout(function(){ window.location.href = 'admin/index.php'; }, 2000);</script>";
                        
                    } else {
                        $error_message = 'Invalid password.';
                    }
                } else {
                    $error_message = 'User not found or inactive.';
                }
                
                $conn->close();
            }
        } catch (Exception $e) {
            $error_message = 'Login error: ' . $e->getMessage();
        }
    }
}

// Check if already logged in
$is_logged_in = isset($_SESSION['admin_user_id']) && isset($_SESSION['admin_username']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin Login - Solomon Islands Scholarship</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            background: linear-gradient(135deg, #2c3e50, #004499);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card border-0">
                    <div class="card-header login-header text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Simple Admin Login
                        </h3>
                        <p class="mb-0 small opacity-75">Solomon Islands Scholarship System</p>
                        <div class="mt-2">
                            <span class="badge bg-warning">Backup Login System</span>
                        </div>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($is_logged_in): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                You are already logged in as: <strong><?php echo htmlspecialchars($_SESSION['admin_username']); ?></strong>
                            </div>
                            <div class="text-center">
                                <a href="admin/index.php" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                                </a>
                                <a href="admin/logout.php" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a>
                            </div>
                        <?php else: ?>
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>Username
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="admin" required autocomplete="username">
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           value="admin123" required autocomplete="current-password">
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                This is a backup login system with direct database connection.
                            </small>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="home.php" class="btn btn-link btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>Back to Home
                            </a>
                            <a href="admin/login.php" class="btn btn-link btn-sm">
                                <i class="fas fa-shield-alt me-1"></i>Main Admin Login
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Credentials Info -->
                <div class="card mt-3 border-success">
                    <div class="card-body">
                        <h6 class="card-title text-success">
                            <i class="fas fa-key me-2"></i>Default Credentials
                        </h6>
                        <p class="card-text small">
                            <strong>Username:</strong> admin<br>
                            <strong>Password:</strong> admin123
                        </p>
                        <div class="small text-muted">
                            <i class="fas fa-database me-1"></i>
                            Direct database connection: Blackpanther707@707
                        </div>
                    </div>
                </div>
                
                <!-- Setup Link -->
                <div class="card mt-3 border-info">
                    <div class="card-body">
                        <h6 class="card-title text-info">
                            <i class="fas fa-tools me-2"></i>Setup & Fix
                        </h6>
                        <p class="card-text small">
                            If login still doesn't work, run the setup script:
                        </p>
                        <a href="admin_setup.php" class="btn btn-info btn-sm">
                            <i class="fas fa-wrench me-1"></i>Run Admin Setup
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
