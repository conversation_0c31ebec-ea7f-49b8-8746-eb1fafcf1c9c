-- Solomon Islands Scholarship Database Schema
-- For importing into existing database: u787474055_solomonislands
-- Remove the CREATE DATABASE commands and USE your existing database

-- Applications table
CREATE TABLE IF NOT EXISTS applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_code VARCHAR(20) NOT NULL,
    selection_status ENUM('Selected', 'Pending', 'Rejected', 'Under Review') NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    email VARCHAR(100),
    phone VARCHAR(20),
    
    -- Province information
    province_of_origin SET(
        'Central', 'Choiseul', 'Guadalcanal', 'Isabel', 'Makira-Ulawa', 
        'Malaita', 'Rennell and Bellona', 'Temotu', 'Western', 'Honiara'
    ),
    current_residential_province SET(
        'Central', 'Choiseul', 'Guadalcanal', 'Isabel', 'Makira-Ulawa', 
        'Malaita', 'Rennell and Bellona', 'Temotu', 'Western', 'Honiara'
    ),
    
    -- Transport information
    mode_of_transport_to_honiara ENUM('Sea', 'Land', 'Air') NOT NULL,
    honiara_pom_route VARCHAR(100),
    
    -- Higher Education Institution
    hei_name SET(
        'University of Papua New Guinea (UPNG)', 
        'University of Technology (UOT)', 
        'Papua New Guinea University of Natural Resources and Environment (PNGUNRE)',
        'Divine Word University (DWU)',
        'Pacific Adventist University (PAU)',
        'University of Goroka (UOG)'
    ) NOT NULL,
    
    -- PNG destination
    png_province ENUM(
        'National Capital District (NCD)', 
        'Morobe', 
        'Western Highlands', 
        'Eastern Highlands',
        'Madang',
        'East New Britain',
        'West New Britain',
        'Manus',
        'New Ireland',
        'Bougainville',
        'Gulf',
        'Central',
        'Milne Bay',
        'Oro',
        'Southern Highlands',
        'Hela',
        'Jiwaka',
        'Chimbu',
        'Western',
        'Enga'
    ) NOT NULL,
    
    -- Travel question (route preference)
    travel_question ENUM(
        'sea-air-air', 
        'land-air-air', 
        'air-direct',
        'sea-land-air',
        'other'
    ),
    
    -- Costing
    estimated_cost DECIMAL(10,2),
    
    -- Additional fields
    academic_qualifications TEXT,
    course_of_study VARCHAR(200),
    motivation_letter TEXT,
    
    -- System fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    INDEX idx_student_code (student_code),
    INDEX idx_selection_status (selection_status),
    INDEX idx_created_at (created_at)
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    full_name VARCHAR(100),
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Insert default admin user (password: admin123)
-- Only insert if the user doesn't already exist
INSERT IGNORE INTO admin_users (username, password_hash, email, full_name) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'System Administrator');

-- Sessions table for admin login
CREATE TABLE IF NOT EXISTS admin_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
);

-- Application status log
CREATE TABLE IF NOT EXISTS application_status_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    old_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    changed_by INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES admin_users(id) ON DELETE SET NULL
);
