<?php
require_once '../config.php';

// Log the logout activity
if (isAdminLoggedIn()) {
    $username = getAdminUsername();
    logActivity("Admin logout: $username", 'INFO');
}

// Clear all session variables
$_SESSION = array();

// Destroy the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Redirect to login page
redirect('login.php');
?>
