<?php
require_once '../config.php';

// Require admin login
requireAdminLogin();

// Get database connection
$db = Database::getInstance();
$conn = $db->getConnection();

// Handle search and filters (same as in index.php)
$search = sanitizeInput($_GET['search'] ?? '');
$status_filter = sanitizeInput($_GET['status'] ?? '');
$province_filter = sanitizeInput($_GET['province'] ?? '');
$hei_filter = sanitizeInput($_GET['hei'] ?? '');

// Build WHERE clause
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR student_code LIKE ? OR email LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "selection_status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if (!empty($province_filter)) {
    $where_conditions[] = "FIND_IN_SET(?, province_of_origin)";
    $params[] = $province_filter;
    $param_types .= 's';
}

if (!empty($hei_filter)) {
    $where_conditions[] = "FIND_IN_SET(?, hei_name)";
    $params[] = $hei_filter;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get all applications matching the criteria
$sql = "SELECT * FROM applications $where_clause ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$applications = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Set headers for Excel download
$filename = 'solomon_scholarship_applications_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Create output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Headers
$headers = [
    'Application ID',
    'Reference Number',
    'Student Code',
    'Selection Status',
    'Full Name',
    'Date of Birth',
    'Gender',
    'Email',
    'Phone',
    'Province of Origin',
    'Current Residential Province',
    'Mode of Transport to Honiara',
    'Honiara-POM Route',
    'Higher Education Institution',
    'PNG Province',
    'Travel Route Preference',
    'Estimated Cost (USD)',
    'Course of Study',
    'Academic Qualifications',
    'Motivation Letter',
    'Submitted Date',
    'Last Updated',
    'IP Address'
];

// Write headers
fputcsv($output, $headers);

// Write data
foreach ($applications as $app) {
    $reference_number = "SI-" . date('Y', strtotime($app['created_at'])) . "-" . str_pad($app['id'], 6, '0', STR_PAD_LEFT);
    
    $row = [
        $app['id'],
        $reference_number,
        $app['student_code'],
        $app['selection_status'],
        $app['full_name'],
        $app['date_of_birth'] ? date('Y-m-d', strtotime($app['date_of_birth'])) : '',
        $app['gender'] ?: '',
        $app['email'] ?: '',
        $app['phone'] ?: '',
        str_replace(',', '; ', $app['province_of_origin']),
        $app['current_residential_province'] ? str_replace(',', '; ', $app['current_residential_province']) : '',
        $app['mode_of_transport_to_honiara'],
        $app['honiara_pom_route'] ?: '',
        str_replace(',', '; ', $app['hei_name']),
        $app['png_province'],
        $app['travel_question'] ?: '',
        $app['estimated_cost'] ?: '',
        $app['course_of_study'] ?: '',
        $app['academic_qualifications'] ?: '',
        $app['motivation_letter'] ?: '',
        date('Y-m-d H:i:s', strtotime($app['created_at'])),
        date('Y-m-d H:i:s', strtotime($app['updated_at'])),
        $app['ip_address']
    ];
    
    fputcsv($output, $row);
}

// Close output stream
fclose($output);

// Log the export activity
$admin_username = getAdminUsername();
$record_count = count($applications);
logActivity("Data export performed by $admin_username: $record_count records exported", 'INFO');

exit();
?>
