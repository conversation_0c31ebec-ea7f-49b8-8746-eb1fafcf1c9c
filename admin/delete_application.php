<?php
/**
 * Delete Application Handler
 * Handles deletion of scholarship applications with proper security checks
 */

require_once '../config.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('login.php');
}

// Check if application ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'Invalid application ID provided.';
    redirect('index.php');
}

$application_id = intval($_GET['id']);

// Check if this is a confirmation request
if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    // Validate CSRF token for actual deletion
    if (!isset($_GET['token']) || !validateCSRFToken($_GET['token'])) {
        $_SESSION['error_message'] = 'Security token validation failed. Please try again.';
        redirect('index.php');
    }
    
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        // First, get application details for logging
        $sql = "SELECT student_code, full_name FROM applications WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $application_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $_SESSION['error_message'] = 'Application not found.';
            redirect('index.php');
        }
        
        $application = $result->fetch_assoc();
        
        // Delete the application
        $delete_sql = "DELETE FROM applications WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $application_id);
        
        if ($delete_stmt->execute()) {
            // Log the deletion
            logActivity("Application deleted: ID $application_id, Student: {$application['student_code']} ({$application['full_name']}) by admin: " . $_SESSION['admin_username'], 'WARNING');
            
            $_SESSION['success_message'] = "Application for {$application['full_name']} (ID: $application_id) has been successfully deleted.";
        } else {
            $_SESSION['error_message'] = 'Failed to delete application. Please try again.';
            logActivity("Failed to delete application ID $application_id by admin: " . $_SESSION['admin_username'], 'ERROR');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'An error occurred while deleting the application.';
        logActivity("Error deleting application ID $application_id: " . $e->getMessage(), 'ERROR');
    }
    
    redirect('index.php');
}

// If not confirmed, show confirmation page
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Get application details
    $sql = "SELECT * FROM applications WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $application_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $_SESSION['error_message'] = 'Application not found.';
        redirect('index.php');
    }
    
    $application = $result->fetch_assoc();
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'An error occurred while retrieving application details.';
    redirect('index.php');
}

// Generate CSRF token for deletion
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Application - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-shield-alt me-2"></i>
                Admin Panel
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? $_SESSION['admin_username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="change_password.php">
                            <i class="fas fa-key me-2"></i>Change Password
                        </a></li>
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user-cog me-2"></i>Profile Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-trash-alt me-2 text-danger"></i>
                        Delete Application
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                            <li class="breadcrumb-item active">Delete Application</li>
                        </ol>
                    </nav>
                </div>

                <!-- Confirmation Card -->
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Confirm Application Deletion
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-warning me-2"></i>Warning: This action cannot be undone!</h6>
                            <p class="mb-0">You are about to permanently delete this scholarship application. All associated data will be lost.</p>
                        </div>

                        <!-- Application Details -->
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Application Details:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Application ID:</strong></td>
                                        <td><?php echo $application['id']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Student Code:</strong></td>
                                        <td><?php echo htmlspecialchars($application['student_code']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td><?php echo htmlspecialchars($application['full_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <?php
                                            $status_class = [
                                                'Selected' => 'success',
                                                'Pending' => 'warning',
                                                'Under Review' => 'info',
                                                'Rejected' => 'danger'
                                            ];
                                            $class = $status_class[$application['selection_status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $class; ?>">
                                                <?php echo htmlspecialchars($application['selection_status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Additional Information:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo htmlspecialchars($application['email'] ?: 'Not provided'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Province:</strong></td>
                                        <td><?php echo htmlspecialchars($application['province_of_origin'] ?: 'Not specified'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>PNG Province:</strong></td>
                                        <td><?php echo htmlspecialchars($application['png_province'] ?: 'Not specified'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Submitted:</strong></td>
                                        <td><?php echo date('M j, Y g:i A', strtotime($application['created_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between mt-4 pt-3 border-top">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            
                            <div>
                                <a href="view.php?id=<?php echo $application['id']; ?>" 
                                   class="btn btn-outline-primary me-2" target="_blank">
                                    <i class="fas fa-eye me-2"></i>View Details
                                </a>
                                
                                <a href="delete_application.php?id=<?php echo $application['id']; ?>&confirm=yes&token=<?php echo $csrf_token; ?>" 
                                   class="btn btn-danger" 
                                   onclick="return confirm('Are you absolutely sure you want to delete this application? This action cannot be undone!');">
                                    <i class="fas fa-trash-alt me-2"></i>Delete Application
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="card mt-4 border-info">
                    <div class="card-body">
                        <h6 class="text-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Security Notice
                        </h6>
                        <ul class="list-unstyled mb-0 small">
                            <li>• This action will be logged for audit purposes</li>
                            <li>• Only authorized administrators can delete applications</li>
                            <li>• Consider changing the status to "Rejected" instead of deletion</li>
                            <li>• Deleted applications cannot be recovered</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
