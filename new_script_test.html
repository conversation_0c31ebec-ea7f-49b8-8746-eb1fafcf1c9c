<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🆕 New Script Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-step { 
            display: none; 
            border: 3px solid #ddd;
            padding: 25px;
            margin: 15px 0;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .form-step.active { 
            display: block; 
            border-color: #28a745;
            background: #ffffff;
            box-shadow: 0 4px 15px rgba(40,167,69,0.2);
        }
        .debug-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ffffff;
            border: 2px solid #28a745;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            max-width: 350px;
            z-index: 1000;
        }
        .console-output {
            background: #1a1a1a;
            color: #00ff41;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 250px;
            overflow-y: auto;
            border: 1px solid #28a745;
        }
        .status-good { color: #28a745; font-weight: bold; }
        .status-bad { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h6><i class="fas fa-rocket me-2"></i>New Script Test</h6>
        <div id="debugStatus">Loading...</div>
        <div class="console-output" id="consoleOutput">Console ready...</div>
        <div class="mt-3">
            <button onclick="testNavigation()" class="btn btn-sm btn-success">
                <i class="fas fa-play me-1"></i>Auto Test
            </button>
            <button onclick="location.reload()" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-refresh me-1"></i>Reload
            </button>
        </div>
    </div>

    <div class="container py-5">
        <div class="text-center mb-4">
            <h1><i class="fas fa-rocket me-2 text-success"></i>New Script Test</h1>
            <p class="lead">Testing the new form-navigation.js file</p>
            <div class="alert alert-success">
                <i class="fas fa-info-circle me-2"></i>
                <strong>This uses a completely new JavaScript file: assets/form-navigation.js</strong>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="progress mb-4" style="height: 15px;">
            <div class="progress-bar bg-success" role="progressbar" style="width: 20%" 
                 aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                <small class="text-white fw-bold">Step 1 of 5</small>
            </div>
        </div>

        <!-- Form -->
        <form id="scholarshipForm" class="card shadow-lg border-success">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Scholarship Application Form</h5>
            </div>
            <div class="card-body">
                <!-- Step 1 -->
                <div class="form-step active" data-step="1">
                    <h3 class="text-success mb-4">
                        <i class="fas fa-user me-2"></i>Step 1: Basic Information
                    </h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="student_code" class="form-label fw-bold">Student Code *</label>
                            <input type="text" class="form-control form-control-lg" id="student_code" 
                                   name="student_code" required placeholder="Enter your student code">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label fw-bold">Full Name *</label>
                            <input type="text" class="form-control form-control-lg" id="full_name" 
                                   name="full_name" required placeholder="Enter your full name">
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        Fill in the required fields and click Next to proceed to Step 2.
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="form-step" data-step="2">
                    <h3 class="text-primary mb-4">
                        <i class="fas fa-map-marker-alt me-2"></i>Step 2: Location Information
                    </h3>
                    <div class="mb-4">
                        <label class="form-label fw-bold">Province of Origin *</label>
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="province_of_origin[]" 
                                           value="Central" id="central">
                                    <label class="form-check-label" for="central">Central</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="province_of_origin[]" 
                                           value="Honiara" id="honiara">
                                    <label class="form-check-label" for="honiara">Honiara</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="province_of_origin[]" 
                                           value="Malaita" id="malaita">
                                    <label class="form-check-label" for="malaita">Malaita</label>
                                </div>
                            </div>
                        </div>
                        <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                            Please select at least one province of origin.
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        You must select at least one province to continue.
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="form-step" data-step="3">
                    <h3 class="text-info mb-4">
                        <i class="fas fa-plane me-2"></i>Step 3: Travel Information
                    </h3>
                    <div class="mb-3">
                        <label for="transport" class="form-label fw-bold">Mode of Transport *</label>
                        <select class="form-select form-select-lg" id="transport" name="transport" required>
                            <option value="">Choose your transport method...</option>
                            <option value="Air">Air</option>
                            <option value="Sea">Sea</option>
                            <option value="Land">Land</option>
                        </select>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="form-step" data-step="4">
                    <h3 class="text-warning mb-4">
                        <i class="fas fa-graduation-cap me-2"></i>Step 4: Academic Information
                    </h3>
                    <div class="mb-3">
                        <label for="qualifications" class="form-label fw-bold">Academic Qualifications *</label>
                        <textarea class="form-control" id="qualifications" name="qualifications" 
                                  rows="4" required placeholder="Describe your academic qualifications..."></textarea>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="form-step" data-step="5">
                    <h3 class="text-danger mb-4">
                        <i class="fas fa-file-signature me-2"></i>Step 5: Final Details
                    </h3>
                    <div class="mb-3">
                        <label for="motivation" class="form-label fw-bold">Motivation Letter *</label>
                        <textarea class="form-control" id="motivation" name="motivation" 
                                  rows="5" required placeholder="Write your motivation letter..."></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                        <label class="form-check-label fw-bold" for="terms">
                            I agree to the terms and conditions *
                        </label>
                    </div>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        You're almost done! Review your information and submit.
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="d-flex justify-content-between mt-4 pt-3 border-top">
                    <button type="button" class="btn btn-secondary btn-lg" id="prevBtn" style="display: none;">
                        <i class="fas fa-arrow-left me-2"></i>Previous
                    </button>
                    <div class="flex-grow-1"></div>
                    <button type="button" class="btn btn-success btn-lg" id="nextBtn">
                        Next<i class="fas fa-arrow-right ms-2"></i>
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" style="display: none;">
                        <i class="fas fa-paper-plane me-2"></i>Submit Application
                    </button>
                </div>
            </div>
        </form>

        <div class="text-center mt-4">
            <a href="application.php" class="btn btn-outline-success">
                <i class="fas fa-external-link-alt me-2"></i>Go to Main Application Form
            </a>
        </div>
    </div>

    <!-- Use the NEW script file -->
    <script src="assets/form-navigation.js"></script>
    
    <script>
        // Enhanced console logging
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : '#00ff41';
            consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Debug status updater
        function updateDebugStatus() {
            const debugStatus = document.getElementById('debugStatus');
            const activeStep = document.querySelector('.form-step.active');
            const nextBtn = document.getElementById('nextBtn');
            
            // Check if new script variables are available
            const scriptLoaded = typeof currentStep !== 'undefined' && typeof displayStep === 'function';
            const eventAttached = nextBtn && nextBtn.onclick;
            
            debugStatus.innerHTML = `
                <div class="mb-2">
                    <span class="${scriptLoaded ? 'status-good' : 'status-bad'}">●</span>
                    <strong>Script:</strong> ${scriptLoaded ? 'Loaded' : 'Not Loaded'}
                </div>
                <div class="mb-2">
                    <span class="${eventAttached ? 'status-good' : 'status-bad'}">●</span>
                    <strong>Events:</strong> ${eventAttached ? 'Attached' : 'Missing'}
                </div>
                <div class="mb-2">
                    <span class="status-good">●</span>
                    <strong>Current:</strong> ${scriptLoaded ? currentStep + '/' + totalSteps : 'N/A'}
                </div>
                <div class="mb-2">
                    <span class="status-good">●</span>
                    <strong>Active:</strong> Step ${activeStep ? activeStep.dataset.step : 'None'}
                </div>
            `;
        }
        
        // Update debug status every second
        setInterval(updateDebugStatus, 1000);
        
        // Initial update
        setTimeout(updateDebugStatus, 500);
        
        console.log('🆕 New script test page loaded!');
    </script>
</body>
</html>
