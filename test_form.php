<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Navigation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-step { display: none; }
        .form-step.active { display: block; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container py-5">
        <h2>Form Navigation Debug Test</h2>
        
        <div class="debug-info">
            <h5>Debug Information</h5>
            <div id="debugInfo">Loading...</div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <!-- Progress Bar -->
                <div class="progress mb-4" style="height: 6px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>

                <form id="scholarshipForm">
                    <!-- Step 1 -->
                    <div class="form-step active" data-step="1">
                        <h4>Step 1: Basic Information</h4>
                        <div class="mb-3">
                            <label for="student_code" class="form-label">Student Code *</label>
                            <input type="text" class="form-control" id="student_code" name="student_code" required>
                        </div>
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="selection_status" class="form-label">Selection Status *</label>
                            <select class="form-select" id="selection_status" name="selection_status" required>
                                <option value="">Choose...</option>
                                <option value="Selected">Selected</option>
                                <option value="Pending">Pending</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 2 -->
                    <div class="form-step" data-step="2">
                        <h4>Step 2: Location Information</h4>
                        <div class="mb-3">
                            <label class="form-label">Province of Origin *</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Central" id="central">
                                <label class="form-check-label" for="central">Central</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Honiara" id="honiara">
                                <label class="form-check-label" for="honiara">Honiara</label>
                            </div>
                            <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                                Please select at least one province of origin.
                            </div>
                        </div>
                    </div>

                    <!-- Step 3 -->
                    <div class="form-step" data-step="3">
                        <h4>Step 3: Final Step</h4>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                            Previous
                        </button>
                        <button type="button" class="btn btn-primary" id="nextBtn">
                            Next
                        </button>
                        <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                            Submit
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="debug-info mt-4">
            <h5>Console Output</h5>
            <div id="consoleOutput" style="font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? 'red' : 'black';
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Update debug info
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const form = document.getElementById('scholarshipForm');
            const steps = document.querySelectorAll('.form-step');
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');
            const submitBtn = document.getElementById('submitBtn');
            
            debugInfo.innerHTML = `
                <strong>Form Elements:</strong><br>
                Form found: ${!!form}<br>
                Steps found: ${steps.length}<br>
                Next button: ${!!nextBtn}<br>
                Previous button: ${!!prevBtn}<br>
                Submit button: ${!!submitBtn}<br>
                Current step: ${window.scholarshipForm ? window.scholarshipForm.currentStep : 'Not initialized'}<br>
                Active step: ${document.querySelector('.form-step.active')?.dataset.step || 'None'}
            `;
        }

        // Load the main script
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            updateDebugInfo();
            
            // Load the main script
            const script = document.createElement('script');
            script.src = 'assets/script.js';
            script.onload = function() {
                console.log('Main script loaded');
                setTimeout(updateDebugInfo, 100);
            };
            script.onerror = function() {
                console.error('Failed to load main script');
            };
            document.head.appendChild(script);
            
            // Update debug info every 2 seconds
            setInterval(updateDebugInfo, 2000);
        });
    </script>
</body>
</html>
