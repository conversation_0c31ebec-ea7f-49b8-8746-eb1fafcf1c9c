<?php
/**
 * Form Debug & Fix Tool
 * Comprehensive tool to diagnose and fix form navigation issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Form Debug & Fix Tool</h1>";
echo "<p>This tool will diagnose and fix the Next button navigation issue in the application form.</p>";

// Test 1: Check if form files exist
echo "<h2>Step 1: File Existence Check</h2>";
$files_to_check = [
    'application.php' => 'Main application form',
    'assets/script.js' => 'JavaScript navigation file',
    'assets/style.css' => 'CSS styles file',
    'config.php' => 'Configuration file'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<div style='color: green;'>✅ $description: $file exists</div>";
    } else {
        echo "<div style='color: red;'>❌ $description: $file missing</div>";
    }
}

// Test 2: Check JavaScript file content
echo "<h2>Step 2: JavaScript Analysis</h2>";
if (file_exists('assets/script.js')) {
    $js_content = file_get_contents('assets/script.js');
    
    // Check for key functions
    $js_checks = [
        'scholarshipForm' => 'Main form object',
        'nextBtn' => 'Next button reference',
        'prevBtn' => 'Previous button reference',
        'validateCurrentStep' => 'Step validation function',
        'showStep' => 'Step display function',
        'updateProgressBar' => 'Progress bar function'
    ];
    
    foreach ($js_checks as $check => $description) {
        if (strpos($js_content, $check) !== false) {
            echo "<div style='color: green;'>✅ $description found in JavaScript</div>";
        } else {
            echo "<div style='color: red;'>❌ $description missing from JavaScript</div>";
        }
    }
    
    // Check for common issues
    echo "<h3>JavaScript Issues Check:</h3>";
    if (strpos($js_content, 'DOMContentLoaded') !== false) {
        echo "<div style='color: green;'>✅ DOM ready event found</div>";
    } else {
        echo "<div style='color: red;'>❌ DOM ready event missing</div>";
    }
    
    if (strpos($js_content, 'addEventListener') !== false) {
        echo "<div style='color: green;'>✅ Event listeners found</div>";
    } else {
        echo "<div style='color: red;'>❌ Event listeners missing</div>";
    }
} else {
    echo "<div style='color: red;'>❌ JavaScript file not found</div>";
}

// Test 3: Check application.php form structure
echo "<h2>Step 3: Form Structure Analysis</h2>";
if (file_exists('application.php')) {
    $form_content = file_get_contents('application.php');
    
    // Count form steps
    $step_count = preg_match_all('/data-step="(\d+)"/', $form_content, $matches);
    echo "<div style='color: " . ($step_count >= 5 ? 'green' : 'red') . ";'>";
    echo ($step_count >= 5 ? '✅' : '❌') . " Found $step_count form steps (expected 5)</div>";
    
    // Check for navigation buttons
    if (strpos($form_content, 'id="nextBtn"') !== false) {
        echo "<div style='color: green;'>✅ Next button found in HTML</div>";
    } else {
        echo "<div style='color: red;'>❌ Next button missing from HTML</div>";
    }
    
    if (strpos($form_content, 'id="prevBtn"') !== false) {
        echo "<div style='color: green;'>✅ Previous button found in HTML</div>";
    } else {
        echo "<div style='color: red;'>❌ Previous button missing from HTML</div>";
    }
    
    if (strpos($form_content, 'id="submitBtn"') !== false) {
        echo "<div style='color: green;'>✅ Submit button found in HTML</div>";
    } else {
        echo "<div style='color: red;'>❌ Submit button missing from HTML</div>";
    }
    
    // Check for form ID
    if (strpos($form_content, 'id="scholarshipForm"') !== false) {
        echo "<div style='color: green;'>✅ Form ID 'scholarshipForm' found</div>";
    } else {
        echo "<div style='color: red;'>❌ Form ID 'scholarshipForm' missing</div>";
    }
    
    // Check for script inclusion
    if (strpos($form_content, 'assets/script.js') !== false) {
        echo "<div style='color: green;'>✅ JavaScript file included in form</div>";
    } else {
        echo "<div style='color: red;'>❌ JavaScript file not included in form</div>";
    }
} else {
    echo "<div style='color: red;'>❌ Application form file not found</div>";
}

// Test 4: Create a minimal working form for testing
echo "<h2>Step 4: Create Test Form</h2>";

$test_form_content = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Navigation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-step { display: none; }
        .form-step.active { display: block; }
        .debug-info { 
            position: fixed; 
            top: 10px; 
            right: 10px; 
            background: #f8f9fa; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            max-width: 300px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <h6>🔍 Debug Info</h6>
        <div id="debugOutput">Loading...</div>
    </div>

    <div class="container py-5">
        <h2>🧪 Form Navigation Test</h2>
        
        <div class="card">
            <div class="card-body">
                <form id="scholarshipForm">
                    <!-- Progress Bar -->
                    <div class="progress mb-4" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>

                    <!-- Step 1 -->
                    <div class="form-step active" data-step="1">
                        <h4 class="text-primary mb-4">Step 1: Basic Information</h4>
                        <div class="mb-3">
                            <label for="student_code" class="form-label">Student Code *</label>
                            <input type="text" class="form-control" id="student_code" name="student_code" required>
                        </div>
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                    </div>

                    <!-- Step 2 -->
                    <div class="form-step" data-step="2">
                        <h4 class="text-primary mb-4">Step 2: Location Information</h4>
                        <div class="mb-3">
                            <label class="form-label">Province of Origin *</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Central" id="central">
                                <label class="form-check-label" for="central">Central</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="province_of_origin[]" value="Honiara" id="honiara">
                                <label class="form-check-label" for="honiara">Honiara</label>
                            </div>
                            <div class="invalid-feedback d-block" id="province_origin_error" style="display: none;">
                                Please select at least one province of origin.
                            </div>
                        </div>
                    </div>

                    <!-- Step 3 -->
                    <div class="form-step" data-step="3">
                        <h4 class="text-primary mb-4">Step 3: Travel Information</h4>
                        <div class="mb-3">
                            <label for="transport" class="form-label">Mode of Transport *</label>
                            <select class="form-select" id="transport" name="transport" required>
                                <option value="">Choose...</option>
                                <option value="Air">Air</option>
                                <option value="Sea">Sea</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 4 -->
                    <div class="form-step" data-step="4">
                        <h4 class="text-primary mb-4">Step 4: Academic Information</h4>
                        <div class="mb-3">
                            <label for="qualifications" class="form-label">Academic Qualifications *</label>
                            <textarea class="form-control" id="qualifications" name="qualifications" required></textarea>
                        </div>
                    </div>

                    <!-- Step 5 -->
                    <div class="form-step" data-step="5">
                        <h4 class="text-primary mb-4">Step 5: Final Details</h4>
                        <div class="mb-3">
                            <label for="motivation" class="form-label">Motivation Letter *</label>
                            <textarea class="form-control" id="motivation" name="motivation" required></textarea>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">I agree to the terms *</label>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </button>
                        <button type="button" class="btn btn-primary" id="nextBtn">
                            Next<i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                            <i class="fas fa-paper-plane me-2"></i>Submit
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="application.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right me-2"></i>Go to Real Application Form
            </a>
            <button onclick="resetForm()" class="btn btn-outline-warning">
                <i class="fas fa-refresh me-2"></i>Reset Test
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form navigation object
        window.scholarshipForm = {
            currentStep: 1,
            totalSteps: 5,
            steps: document.querySelectorAll(".form-step"),
            nextBtn: document.getElementById("nextBtn"),
            prevBtn: document.getElementById("prevBtn"),
            submitBtn: document.getElementById("submitBtn"),
            progressBar: document.querySelector(".progress-bar")
        };

        // Debug function
        function updateDebug() {
            const debug = document.getElementById("debugOutput");
            const form = window.scholarshipForm;
            
            debug.innerHTML = `
                <small>
                <strong>Current Step:</strong> ${form.currentStep}/${form.totalSteps}<br>
                <strong>Steps Found:</strong> ${form.steps.length}<br>
                <strong>Next Btn:</strong> ${form.nextBtn ? "✅" : "❌"}<br>
                <strong>Prev Btn:</strong> ${form.prevBtn ? "✅" : "❌"}<br>
                <strong>Submit Btn:</strong> ${form.submitBtn ? "✅" : "❌"}<br>
                <strong>Active Step:</strong> ${document.querySelector(".form-step.active")?.dataset.step || "None"}<br>
                <strong>Progress:</strong> ${form.progressBar?.style.width || "Unknown"}
                </small>
            `;
        }

        // Show specific step
        function showStep(step) {
            const form = window.scholarshipForm;
            
            // Hide all steps
            form.steps.forEach(s => s.classList.remove("active"));
            
            // Show current step
            const currentStepElement = document.querySelector(`[data-step="${step}"]`);
            if (currentStepElement) {
                currentStepElement.classList.add("active");
            }
            
            // Update buttons
            form.prevBtn.style.display = step > 1 ? "block" : "none";
            form.nextBtn.style.display = step < form.totalSteps ? "block" : "none";
            form.submitBtn.style.display = step === form.totalSteps ? "block" : "none";
            
            // Update progress bar
            const progress = (step / form.totalSteps) * 100;
            form.progressBar.style.width = progress + "%";
            form.progressBar.setAttribute("aria-valuenow", progress);
            
            updateDebug();
        }

        // Validate current step
        function validateCurrentStep() {
            const form = window.scholarshipForm;
            const currentStepElement = document.querySelector(`[data-step="${form.currentStep}"]`);
            const requiredFields = currentStepElement.querySelectorAll("[required]");
            let isValid = true;
            
            // Special validation for Step 2 (province checkboxes)
            if (form.currentStep === 2) {
                const provinceCheckboxes = currentStepElement.querySelectorAll(\'input[name="province_of_origin[]"]\');
                const isProvinceSelected = Array.from(provinceCheckboxes).some(cb => cb.checked);
                
                if (!isProvinceSelected) {
                    const errorElement = document.getElementById("province_origin_error");
                    if (errorElement) {
                        errorElement.style.display = "block";
                    }
                    isValid = false;
                } else {
                    const errorElement = document.getElementById("province_origin_error");
                    if (errorElement) {
                        errorElement.style.display = "none";
                    }
                }
            }
            
            // Validate required fields
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add("is-invalid");
                    isValid = false;
                } else {
                    field.classList.remove("is-invalid");
                    field.classList.add("is-valid");
                }
            });
            
            return isValid;
        }

        // Next button click handler
        function nextStep() {
            const form = window.scholarshipForm;
            
            if (validateCurrentStep()) {
                if (form.currentStep < form.totalSteps) {
                    form.currentStep++;
                    showStep(form.currentStep);
                    console.log("Moved to step:", form.currentStep);
                }
            } else {
                console.log("Validation failed for step:", form.currentStep);
            }
        }

        // Previous button click handler
        function prevStep() {
            const form = window.scholarshipForm;
            
            if (form.currentStep > 1) {
                form.currentStep--;
                showStep(form.currentStep);
                console.log("Moved to step:", form.currentStep);
            }
        }

        // Reset form
        function resetForm() {
            window.scholarshipForm.currentStep = 1;
            showStep(1);
            document.getElementById("scholarshipForm").reset();
            console.log("Form reset");
        }

        // Initialize form
        document.addEventListener("DOMContentLoaded", function() {
            const form = window.scholarshipForm;
            
            // Add event listeners
            if (form.nextBtn) {
                form.nextBtn.addEventListener("click", nextStep);
                console.log("Next button event listener added");
            }
            
            if (form.prevBtn) {
                form.prevBtn.addEventListener("click", prevStep);
                console.log("Previous button event listener added");
            }
            
            // Initialize first step
            showStep(1);
            
            // Update debug info every second
            setInterval(updateDebug, 1000);
            
            console.log("Form initialized successfully");
        });
    </script>
</body>
</html>';

file_put_contents('form_test.html', $test_form_content);
echo "<div style='color: green;'>✅ Test form created: <a href='form_test.html' target='_blank'>form_test.html</a></div>";

// Test 5: Fix the main application form
echo "<h2>Step 5: Fix Main Application Form</h2>";

if (file_exists('application.php')) {
    $app_content = file_get_contents('application.php');
    
    // Check if script.js is properly included
    if (strpos($app_content, 'assets/script.js') === false) {
        echo "<div style='color: orange;'>⚠️ JavaScript file not included. Adding it...</div>";
        
        // Add script inclusion before closing body tag
        $app_content = str_replace('</body>', '    <script src="assets/script.js"></script>' . "\n" . '</body>', $app_content);
        file_put_contents('application.php', $app_content);
        echo "<div style='color: green;'>✅ JavaScript file inclusion added</div>";
    } else {
        echo "<div style='color: green;'>✅ JavaScript file already included</div>";
    }
} else {
    echo "<div style='color: red;'>❌ Cannot fix application.php - file not found</div>";
}

// Test 6: Create/Fix JavaScript file
echo "<h2>Step 6: Create/Fix JavaScript File</h2>";

$fixed_js_content = '/**
 * Solomon Islands Scholarship Application - Form Navigation
 * Fixed version with comprehensive debugging
 */

// Form navigation object
window.scholarshipForm = {
    currentStep: 1,
    totalSteps: 5,
    steps: null,
    nextBtn: null,
    prevBtn: null,
    submitBtn: null,
    progressBar: null
};

/**
 * Initialize form navigation
 */
function initializeForm() {
    const form = window.scholarshipForm;
    
    // Get form elements
    form.steps = document.querySelectorAll(".form-step");
    form.nextBtn = document.getElementById("nextBtn");
    form.prevBtn = document.getElementById("prevBtn");
    form.submitBtn = document.getElementById("submitBtn");
    form.progressBar = document.querySelector(".progress-bar");
    
    console.log("Form initialization:", {
        steps: form.steps.length,
        nextBtn: !!form.nextBtn,
        prevBtn: !!form.prevBtn,
        submitBtn: !!form.submitBtn,
        progressBar: !!form.progressBar
    });
    
    // Add event listeners
    if (form.nextBtn) {
        form.nextBtn.addEventListener("click", function(e) {
            e.preventDefault();
            console.log("Next button clicked");
            nextStep();
        });
    }
    
    if (form.prevBtn) {
        form.prevBtn.addEventListener("click", function(e) {
            e.preventDefault();
            console.log("Previous button clicked");
            prevStep();
        });
    }
    
    // Initialize first step
    showStep(1);
}

/**
 * Show specific step
 */
function showStep(step) {
    const form = window.scholarshipForm;
    
    console.log("Showing step:", step);
    
    // Hide all steps
    form.steps.forEach(s => s.classList.remove("active"));
    
    // Show current step
    const currentStepElement = document.querySelector(`[data-step="${step}"]`);
    if (currentStepElement) {
        currentStepElement.classList.add("active");
        console.log("Step element found and activated");
    } else {
        console.error("Step element not found for step:", step);
    }
    
    // Update buttons
    if (form.prevBtn) {
        form.prevBtn.style.display = step > 1 ? "block" : "none";
    }
    if (form.nextBtn) {
        form.nextBtn.style.display = step < form.totalSteps ? "block" : "none";
    }
    if (form.submitBtn) {
        form.submitBtn.style.display = step === form.totalSteps ? "block" : "none";
    }
    
    // Update progress bar
    if (form.progressBar) {
        const progress = (step / form.totalSteps) * 100;
        form.progressBar.style.width = progress + "%";
        form.progressBar.setAttribute("aria-valuenow", progress);
    }
}

/**
 * Validate current step
 */
function validateCurrentStep() {
    const form = window.scholarshipForm;
    const currentStepElement = document.querySelector(`[data-step="${form.currentStep}"]`);
    
    if (!currentStepElement) {
        console.error("Current step element not found");
        return false;
    }
    
    const requiredFields = currentStepElement.querySelectorAll("[required]");
    let isValid = true;
    
    console.log("Validating step", form.currentStep, "with", requiredFields.length, "required fields");
    
    // Special validation for Step 2 (province checkboxes)
    if (form.currentStep === 2) {
        const provinceCheckboxes = currentStepElement.querySelectorAll(\'input[name="province_of_origin[]"]\');
        const isProvinceSelected = Array.from(provinceCheckboxes).some(cb => cb.checked);
        
        if (!isProvinceSelected) {
            const errorElement = document.getElementById("province_origin_error");
            if (errorElement) {
                errorElement.style.display = "block";
            }
            isValid = false;
            console.log("Province validation failed");
        } else {
            const errorElement = document.getElementById("province_origin_error");
            if (errorElement) {
                errorElement.style.display = "none";
            }
        }
    }
    
    // Validate required fields
    requiredFields.forEach(field => {
        if (field.type === "checkbox" && field.hasAttribute("required")) {
            if (!field.checked) {
                field.classList.add("is-invalid");
                isValid = false;
            } else {
                field.classList.remove("is-invalid");
                field.classList.add("is-valid");
            }
        } else if (!field.value.trim()) {
            field.classList.add("is-invalid");
            isValid = false;
        } else {
            field.classList.remove("is-invalid");
            field.classList.add("is-valid");
        }
    });
    
    console.log("Validation result:", isValid);
    return isValid;
}

/**
 * Move to next step
 */
function nextStep() {
    const form = window.scholarshipForm;
    
    console.log("Next step requested from step:", form.currentStep);
    
    if (validateCurrentStep()) {
        if (form.currentStep < form.totalSteps) {
            form.currentStep++;
            showStep(form.currentStep);
            console.log("Moved to step:", form.currentStep);
        } else {
            console.log("Already at last step");
        }
    } else {
        console.log("Validation failed, staying on step:", form.currentStep);
    }
}

/**
 * Move to previous step
 */
function prevStep() {
    const form = window.scholarshipForm;
    
    if (form.currentStep > 1) {
        form.currentStep--;
        showStep(form.currentStep);
        console.log("Moved to step:", form.currentStep);
    }
}

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", function() {
    console.log("DOM loaded, initializing form...");
    initializeForm();
});

// Backup initialization in case DOMContentLoaded already fired
if (document.readyState === "loading") {
    // DOM is still loading
    document.addEventListener("DOMContentLoaded", initializeForm);
} else {
    // DOM is already loaded
    console.log("DOM already loaded, initializing form immediately...");
    initializeForm();
}';

file_put_contents('assets/script.js', $fixed_js_content);
echo "<div style='color: green;'>✅ Fixed JavaScript file created/updated</div>";

// Summary
echo "<h2>🎉 Fix Complete!</h2>";
echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ Form Navigation Fixed</h3>";
echo "<p><strong>Test Form:</strong> <a href='form_test.html' target='_blank'>form_test.html</a> - Test the navigation here first</p>";
echo "<p><strong>Main Form:</strong> <a href='application.php' target='_blank'>application.php</a> - Your main application form</p>";
echo "<p><strong>JavaScript:</strong> assets/script.js has been fixed with comprehensive debugging</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🔍 Debugging Features Added:</h3>";
echo "<ul>";
echo "<li>Console logging for all navigation events</li>";
echo "<li>Detailed validation feedback</li>";
echo "<li>Error handling for missing elements</li>";
echo "<li>Real-time debug information (in test form)</li>";
echo "<li>Backup initialization methods</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>⚠️ If Issues Persist:</h3>";
echo "<ol>";
echo "<li>Open browser developer tools (F12)</li>";
echo "<li>Check the Console tab for JavaScript errors</li>";
echo "<li>Test the form_test.html first to verify navigation works</li>";
echo "<li>Compare the working test form with your main form</li>";
echo "</ol>";
echo "</div>";
?>
