<?php
/**
 * Test page to verify the status API is working
 * This is a temporary file for testing purposes
 */

require_once 'config.php';

// Get database connection to check if there are any registrations
$db = Database::getInstance();
$conn = $db->getConnection();

// Get some sample registrations
$sql = "SELECT id, student_code, full_name, selection_status, created_at FROM applications ORDER BY created_at DESC LIMIT 5";
$result = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Status API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <h1>Status API Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Sample Registrations in Database</h3>
                <?php if ($result && $result->num_rows > 0): ?>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Student Code</th>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Reference Number</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $result->fetch_assoc()): ?>
                                <?php 
                                $year = date('Y', strtotime($row['created_at']));
                                $ref_number = "SI-{$year}-" . str_pad($row['id'], 6, '0', STR_PAD_LEFT);
                                ?>
                                <tr>
                                    <td><?php echo $row['id']; ?></td>
                                    <td><?php echo htmlspecialchars($row['student_code']); ?></td>
                                    <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $row['selection_status'] === 'Selected' ? 'success' : 'warning'; ?>">
                                            <?php echo $row['selection_status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <code style="cursor: pointer;" onclick="copyToForm('<?php echo $ref_number; ?>', '<?php echo htmlspecialchars($row['student_code']); ?>')" title="Click to copy to form">
                                            <?php echo $ref_number; ?>
                                        </code>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="alert alert-info">
                        <h5>No registrations found</h5>
                        <p>There are no registrations in the database yet. You can:</p>
                        <ol>
                            <li>Go to <a href="application.php">the registration form</a> and submit a test registration</li>
                            <li>Or manually insert test data into the database</li>
                        </ol>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="col-md-6">
                <h3>Test Status API</h3>
                <form id="testForm">
                    <div class="mb-3">
                        <label for="reference_number" class="form-label">Reference Number</label>
                        <input type="text" class="form-control" id="reference_number" 
                               placeholder="SI-2024-000001" required>
                        <div class="form-text">Use a reference number from the table on the left</div>
                    </div>
                    <div class="mb-3">
                        <label for="student_code" class="form-label">Student Code</label>
                        <input type="text" class="form-control" id="student_code" 
                               placeholder="Enter student code" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Test Debug API</button>
                    <button type="button" class="btn btn-outline-primary" onclick="testRegularAPI()">Test Regular API</button>
                </form>
                
                <div id="result" class="mt-4" style="display: none;">
                    <h5>API Response:</h5>
                    <pre id="response" class="bg-light p-3 rounded"></pre>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="status.php" class="btn btn-success">Go to Actual Status Page</a>
            <a href="application.php" class="btn btn-outline-primary">Submit Test Registration</a>
        </div>
    </div>

    <script>
        function testAPI(endpoint) {
            const referenceNumber = document.getElementById('reference_number').value.trim();
            const studentCode = document.getElementById('student_code').value.trim();

            if (!referenceNumber || !studentCode) {
                alert('Please fill in both fields');
                return;
            }

            // Test the API
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reference_number: referenceNumber,
                    student_code: studentCode
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('response').textContent = JSON.stringify(data, null, 2);
                document.getElementById('result').style.display = 'block';
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('response').textContent = 'Error: ' + error.message;
                document.getElementById('result').style.display = 'block';
            });
        }

        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            testAPI('api/debug_status.php');
        });

        function testRegularAPI() {
            testAPI('api/check_status.php');
        }

        function copyToForm(refNumber, studentCode) {
            document.getElementById('reference_number').value = refNumber;
            document.getElementById('student_code').value = studentCode;
            alert('Reference number and student code copied to form!');
        }
    </script>
</body>
</html>
