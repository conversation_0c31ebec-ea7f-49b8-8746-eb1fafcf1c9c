# Admin Login Guide - Solomon Islands Scholarship Application

## How to Access Admin Panel

### Step 1: Navigate to Admin Login
Open your web browser and go to:
```
https://waghitech.com/solomonislands/admin/login.php
```

### Step 2: Login Credentials
Use these default credentials:

**Username:** `admin`  
**Password:** `admin123`

### Step 3: After Login
Once logged in, you'll be redirected to the admin dashboard where you can:

- ✅ **View all applications** in a table format
- ✅ **Search and filter** applications by status, province, etc.
- ✅ **View detailed application** information
- ✅ **Update application status** (Selected, Pending, Under Review, Rejected)
- ✅ **Export data** to Excel/CSV format
- ✅ **See statistics** (total applications, selected, pending, etc.)

## Admin Dashboard Features

### 1. Statistics Cards
- **Total Applications**: Shows total number of submissions
- **Selected**: Number of approved applications
- **Pending Review**: Applications waiting for decision
- **Rejected**: Declined applications

### 2. Search and Filter
- **Search by**: Name, Student Code, Email
- **Filter by**: Status, Province, Institution
- **Export**: Download filtered results as Excel/CSV

### 3. Application Management
- **View Details**: Click the eye icon to see full application
- **Update Status**: Click the edit icon to change status
- **Email Notifications**: Automatic emails sent when status changes

### 4. Data Export
- **Excel/CSV Export**: Download all or filtered applications
- **Includes all fields**: Personal info, academic details, preferences

## Security Notes

### ⚠️ Important Security Steps:

1. **Change Default Password Immediately**
   - After first login, change the password from `admin123`
   - Use a strong password with letters, numbers, and symbols

2. **Logout When Done**
   - Always click "Logout" when finished
   - Don't leave admin panel open on shared computers

3. **Access Control**
   - Only authorized personnel should have admin access
   - Don't share login credentials

## Troubleshooting

### If you can't access admin login:
1. **Check URL**: Make sure you're going to `/admin/login.php`
2. **Database**: Ensure database tables are imported correctly
3. **File Permissions**: Check that admin files are uploaded

### If login fails:
1. **Credentials**: Double-check username: `admin`, password: `admin123`
2. **Database**: Verify `admin_users` table exists and has data
3. **Session**: Clear browser cookies/cache

### If you see errors:
1. **Database Connection**: Check if database is properly configured
2. **File Paths**: Ensure all files are in correct directories
3. **Permissions**: Check file/folder permissions on server

## Admin Panel URLs

- **Login**: `/admin/login.php`
- **Dashboard**: `/admin/index.php` (after login)
- **Logout**: `/admin/logout.php`

## Default Admin Account Details

The system creates a default admin account with these details:

```sql
Username: admin
Password: admin123 (hashed in database)
Email: <EMAIL>
Full Name: System Administrator
```

## Changing Admin Password

### Method 1: Through Database (Recommended)
1. **Generate new password hash**:
   ```php
   <?php echo password_hash('your_new_password', PASSWORD_DEFAULT); ?>
   ```

2. **Update in database**:
   ```sql
   UPDATE admin_users 
   SET password_hash = 'new_hash_here' 
   WHERE username = 'admin';
   ```

### Method 2: Create New Admin User
1. **Add new admin** through database:
   ```sql
   INSERT INTO admin_users (username, password_hash, email, full_name) 
   VALUES ('newadmin', 'password_hash_here', '<EMAIL>', 'Admin Name');
   ```

## Support

If you need help:
1. **Check database setup**: Ensure all tables are created
2. **Verify file upload**: All admin files should be in `/admin/` folder
3. **Test basic access**: Try accessing `/admin/login.php` directly
4. **Check error logs**: Look for PHP errors in server logs

---

**Remember**: Always change the default password `admin123` to something secure before using in production!
