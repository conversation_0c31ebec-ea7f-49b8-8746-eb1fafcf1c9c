<?php
/**
 * Solomon Islands Student Registration - Form Submission Handler
 *
 * This file processes the student registration form submission
 */

require_once 'config.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    showError('Invalid request method.');
    redirect('index.php');
}

// Validate CSRF token
if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
    showError('Security token validation failed. Please try again.');
    redirect('index.php');
}

try {
    // Get database connection
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Validate and sanitize input data
    $data = validateAndSanitizeInput($_POST);
    
    if ($data === false) {
        redirect('index.php');
    }
    
    // Prepare SQL statement
    $sql = "INSERT INTO applications (
        student_code, selection_status, full_name, date_of_birth, gender, 
        email, phone, province_of_origin, current_residential_province,
        mode_of_transport_to_honiara, honiara_pom_route, hei_name, png_province,
        travel_question, estimated_cost, academic_qualifications, course_of_study,
        motivation_letter, ip_address, user_agent
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    // Bind parameters
    $stmt->bind_param(
        "ssssssssssssssdsssss",
        $data['student_code'],
        $data['selection_status'],
        $data['full_name'],
        $data['date_of_birth'],
        $data['gender'],
        $data['email'],
        $data['phone'],
        $data['province_of_origin'],
        $data['current_residential_province'],
        $data['mode_of_transport_to_honiara'],
        $data['honiara_pom_route'],
        $data['hei_name'],
        $data['png_province'],
        $data['travel_question'],
        $data['estimated_cost'],
        $data['academic_qualifications'],
        $data['course_of_study'],
        $data['motivation_letter'],
        $data['ip_address'],
        $data['user_agent']
    );
    
    // Execute the statement
    if ($stmt->execute()) {
        $application_id = $conn->insert_id;
        
        // Log the successful submission
        logActivity("New student registration submitted - ID: $application_id, Student: {$data['full_name']}", 'INFO');

        // Store application ID in session for thank you page
        $_SESSION['application_id'] = $application_id;
        $_SESSION['student_name'] = $data['full_name'];
        
        // Send confirmation email if email is provided
        if (!empty($data['email'])) {
            sendConfirmationEmail($data['email'], $data['full_name'], $application_id);
        }
        
        // Redirect to thank you page
        redirect('thank_you.php');
        
    } else {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
} catch (Exception $e) {
    // Log the error
    logActivity("Student registration submission failed: " . $e->getMessage(), 'ERROR');

    // Show user-friendly error message
    showError('An error occurred while submitting your registration. Please try again.');
    redirect('index.php');
}

/**
 * Validate and sanitize input data
 */
function validateAndSanitizeInput($post_data) {
    $errors = [];
    $data = [];
    
    // Required fields validation
    $required_fields = [
        'student_code' => 'Student Code',
        'full_name' => 'Full Name',
        'mode_of_transport_to_honiara' => 'Mode of Transport to Honiara',
        'png_province' => 'PNG Province'
    ];

    // Set selection status to 'Pending' for initial submission
    $data['selection_status'] = 'Pending';
    
    foreach ($required_fields as $field => $label) {
        if (empty($post_data[$field])) {
            $errors[] = "$label is required.";
        } else {
            $data[$field] = sanitizeInput($post_data[$field]);
        }
    }
    
    // Validate province of origin (at least one must be selected)
    if (empty($post_data['province_of_origin']) || !is_array($post_data['province_of_origin'])) {
        $errors[] = "Province of Origin is required.";
    } else {
        $data['province_of_origin'] = implode(',', array_map('sanitizeInput', $post_data['province_of_origin']));
    }
    
    // Validate HEI name (at least one must be selected)
    if (empty($post_data['hei_name']) || !is_array($post_data['hei_name'])) {
        $errors[] = "Higher Education Institution is required.";
    } else {
        $data['hei_name'] = implode(',', array_map('sanitizeInput', $post_data['hei_name']));
    }
    
    // Optional fields
    $optional_fields = [
        'date_of_birth', 'gender', 'email', 'phone', 'honiara_pom_route',
        'travel_question', 'estimated_cost', 'academic_qualifications',
        'course_of_study', 'motivation_letter'
    ];
    
    foreach ($optional_fields as $field) {
        $data[$field] = isset($post_data[$field]) ? sanitizeInput($post_data[$field]) : null;
    }
    
    // Handle current residential province (optional)
    if (!empty($post_data['current_residential_province']) && is_array($post_data['current_residential_province'])) {
        $data['current_residential_province'] = implode(',', array_map('sanitizeInput', $post_data['current_residential_province']));
    } else {
        $data['current_residential_province'] = null;
    }
    
    // Validate email format if provided
    if (!empty($data['email']) && !validateEmail($data['email'])) {
        $errors[] = "Please provide a valid email address.";
    }
    
    // Validate phone format if provided
    if (!empty($data['phone']) && !validatePhone($data['phone'])) {
        $errors[] = "Please provide a valid phone number.";
    }
    
    // Validate date of birth if provided
    if (!empty($data['date_of_birth'])) {
        $date = DateTime::createFromFormat('Y-m-d', $data['date_of_birth']);
        if (!$date || $date->format('Y-m-d') !== $data['date_of_birth']) {
            $errors[] = "Please provide a valid date of birth.";
        }
        
        // Check if age is reasonable (between 15 and 50)
        $age = (new DateTime())->diff($date)->y;
        if ($age < 15 || $age > 50) {
            $errors[] = "Please check your date of birth. Age should be between 15 and 50 years.";
        }
    }
    
    // Validate estimated cost if provided
    if (!empty($data['estimated_cost'])) {
        if (!is_numeric($data['estimated_cost']) || $data['estimated_cost'] < 0) {
            $errors[] = "Please provide a valid estimated cost.";
        }
    }
    
    // Validate terms agreement
    if (empty($post_data['terms_agreement'])) {
        $errors[] = "You must agree to the terms and conditions.";
    }
    
    // Add system data
    $data['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $data['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    // If there are errors, show them and return false
    if (!empty($errors)) {
        foreach ($errors as $error) {
            showError($error);
        }
        return false;
    }
    
    return $data;
}

/**
 * Send confirmation email to applicant
 */
function sendConfirmationEmail($email, $name, $application_id) {
    try {
        $subject = "Registration Received - Solomon Islands Student Program";
        $reference_number = "SI-" . date('Y') . "-" . str_pad($application_id, 6, '0', STR_PAD_LEFT);

        $message = "
        <html>
        <head>
            <title>Registration Received</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background-color: #0066cc; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .status-box { background-color: #ffc107; color: #212529; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; }
                .info-box { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #0066cc; }
                .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h2>Solomon Islands Student Registration</h2>
                <p>Papua New Guinea Higher Education Program</p>
            </div>

            <div class='content'>
                <p>Dear $name,</p>

                <p>Thank you for submitting your student registration for the Papua New Guinea Higher Education Program. We have successfully received your information.</p>

                <div class='status-box'>
                    <h3>📋 Registration Status: PENDING</h3>
                    <p>Your registration is currently being processed by our team.</p>
                </div>

                <div class='info-box'>
                    <p><strong>📋 Registration Details:</strong></p>
                    <ul>
                        <li><strong>Reference Number:</strong> $reference_number</li>
                        <li><strong>Registration ID:</strong> $application_id</li>
                        <li><strong>Submitted:</strong> " . date('F j, Y \a\t g:i A') . "</li>
                    </ul>
                </div>

                <div class='info-box'>
                    <p><strong>🔄 What Happens Next:</strong></p>
                    <ol>
                        <li><strong>Processing:</strong> Our team will review your registration information</li>
                        <li><strong>Status Update:</strong> You will receive an email notification when your status changes</li>
                        <li><strong>Travel Arrangements:</strong> Once approved, we will contact you with travel details</li>
                    </ol>
                </div>

                <p><strong>Important:</strong> Keep your reference number safe: <strong>$reference_number</strong></p>
                <p>You can check your registration status anytime using your reference number and student code.</p>

                <p>Best regards,<br>
                <strong>Solomon Islands Student Registration Team</strong><br>
                Department of Higher Education Research Science & Technology (DHERST)<br>
                Papua New Guinea Government</p>
            </div>

            <div class='footer'>
                <p>&copy; 2024 Solomon Islands Government - Papua New Guinea Student Registration Program</p>
                <p>This is an automated message from a no-reply email address.</p>
            </div>
        </body>
        </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . FROM_NAME . " <" . FROM_EMAIL . ">" . "\r\n";
        
        // Send email (note: this requires proper mail server configuration)
        if (mail($email, $subject, $message, $headers)) {
            logActivity("Pending registration email sent to: $email", 'INFO');
        } else {
            logActivity("Failed to send pending registration email to: $email", 'WARNING');
        }
        
    } catch (Exception $e) {
        logActivity("Email sending error: " . $e->getMessage(), 'ERROR');
    }
}
?>
