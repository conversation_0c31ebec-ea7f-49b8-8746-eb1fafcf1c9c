<?php
/**
 * Debug Form Submission
 * This will help us see what's happening when the form is submitted
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>🔍 Form Submission Debug</h1>";

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Step 1: Form Data Received</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ POST Data Received</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
    
    // Check database connection
    echo "<h2>Step 2: Database Connection</h2>";
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        echo "<div style='color: green;'>✅ Database connection successful</div>";
        
        // Check if applications table exists
        $result = $conn->query("SHOW TABLES LIKE 'applications'");
        if ($result->num_rows > 0) {
            echo "<div style='color: green;'>✅ Applications table exists</div>";
            
            // Show table structure
            $structure = $conn->query("DESCRIBE applications");
            echo "<h3>Table Structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['Field']}</td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "<td>{$row['Default']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div style='color: red;'>❌ Applications table does not exist</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</div>";
    }
    
    // Test data validation
    echo "<h2>Step 3: Data Validation Test</h2>";
    
    // Check required fields from the form
    $form_fields = [
        'student_code' => 'Student Code',
        'full_name' => 'Full Name',
        'mode_of_transport_to_honiara' => 'Mode of Transport',
        'png_province' => 'PNG Province',
        'academic_qualifications' => 'Academic Qualifications',
        'motivation_letter' => 'Motivation Letter'
    ];
    
    $missing_fields = [];
    $present_fields = [];
    
    foreach ($form_fields as $field => $label) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $label;
        } else {
            $present_fields[] = $label;
        }
    }
    
    if (!empty($present_fields)) {
        echo "<div style='color: green;'>";
        echo "<h4>✅ Fields Present:</h4>";
        echo "<ul>";
        foreach ($present_fields as $field) {
            echo "<li>$field</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($missing_fields)) {
        echo "<div style='color: red;'>";
        echo "<h4>❌ Missing Required Fields:</h4>";
        echo "<ul>";
        foreach ($missing_fields as $field) {
            echo "<li>$field</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Check province arrays
    echo "<h2>Step 4: Province Data Check</h2>";
    if (isset($_POST['province_of_origin']) && is_array($_POST['province_of_origin'])) {
        echo "<div style='color: green;'>✅ Province of origin data: " . implode(', ', $_POST['province_of_origin']) . "</div>";
    } else {
        echo "<div style='color: red;'>❌ Province of origin data missing or invalid</div>";
    }
    
    // Test simple insertion
    echo "<h2>Step 5: Test Database Insertion</h2>";
    
    if (empty($missing_fields)) {
        try {
            // Prepare simplified data
            $test_data = [
                'student_code' => sanitizeInput($_POST['student_code']),
                'full_name' => sanitizeInput($_POST['full_name']),
                'province_of_origin' => isset($_POST['province_of_origin']) ? implode(',', $_POST['province_of_origin']) : '',
                'mode_of_transport_to_honiara' => sanitizeInput($_POST['mode_of_transport_to_honiara']),
                'png_province' => sanitizeInput($_POST['png_province']),
                'academic_qualifications' => sanitizeInput($_POST['academic_qualifications']),
                'motivation_letter' => sanitizeInput($_POST['motivation_letter']),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ];
            
            // Simple insert query
            $sql = "INSERT INTO applications (
                student_code, full_name, province_of_origin, 
                mode_of_transport_to_honiara, png_province, 
                academic_qualifications, motivation_letter, 
                ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $conn->prepare($sql);
            
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }
            
            $stmt->bind_param(
                "sssssssss",
                $test_data['student_code'],
                $test_data['full_name'],
                $test_data['province_of_origin'],
                $test_data['mode_of_transport_to_honiara'],
                $test_data['png_province'],
                $test_data['academic_qualifications'],
                $test_data['motivation_letter'],
                $test_data['ip_address'],
                $test_data['user_agent']
            );
            
            if ($stmt->execute()) {
                $application_id = $conn->insert_id;
                echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Application inserted with ID: $application_id</div>";
                
                // Show inserted data
                $check_sql = "SELECT * FROM applications WHERE id = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("i", $application_id);
                $check_stmt->execute();
                $result = $check_stmt->get_result();
                $inserted_data = $result->fetch_assoc();
                
                echo "<h4>Inserted Data:</h4>";
                echo "<pre>" . print_r($inserted_data, true) . "</pre>";
                
            } else {
                throw new Exception("Execute failed: " . $stmt->error);
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red; font-weight: bold;'>❌ INSERTION FAILED: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div style='color: orange;'>⚠️ Skipping insertion test due to missing required fields</div>";
    }
    
} else {
    // Show a test form
    echo "<h2>Test Form Submission</h2>";
    echo "<p>Submit the main application form and then come back to this page to see the debug information.</p>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Instructions:</h4>";
    echo "<ol>";
    echo "<li>Go to the <a href='application.php' target='_blank'>main application form</a></li>";
    echo "<li>Fill out all required fields</li>";
    echo "<li>Submit the form</li>";
    echo "<li>If there's an error, check this debug page</li>";
    echo "</ol>";
    echo "</div>";
    
    // Show current database status
    echo "<h2>Current Database Status</h2>";
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        echo "<div style='color: green;'>✅ Database connection working</div>";
        
        // Check applications table
        $result = $conn->query("SHOW TABLES LIKE 'applications'");
        if ($result->num_rows > 0) {
            echo "<div style='color: green;'>✅ Applications table exists</div>";
            
            // Count existing applications
            $count_result = $conn->query("SELECT COUNT(*) as count FROM applications");
            $count = $count_result->fetch_assoc()['count'];
            echo "<div style='color: blue;'>📊 Current applications in database: $count</div>";
        } else {
            echo "<div style='color: red;'>❌ Applications table does not exist</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Database error: " . $e->getMessage() . "</div>";
    }
}

// Create a simple test form
echo "<h2>Simple Test Form</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 10px 0;'>";
echo "<form method='POST' action='debug_submit.php'>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Student Code:</label><br>";
echo "<input type='text' name='student_code' value='TEST001' required style='width: 200px; padding: 5px;'>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Full Name:</label><br>";
echo "<input type='text' name='full_name' value='Test Student' required style='width: 200px; padding: 5px;'>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Province of Origin:</label><br>";
echo "<input type='checkbox' name='province_of_origin[]' value='Central' checked> Central<br>";
echo "<input type='checkbox' name='province_of_origin[]' value='Honiara'> Honiara";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Mode of Transport:</label><br>";
echo "<select name='mode_of_transport_to_honiara' required style='width: 200px; padding: 5px;'>";
echo "<option value='Air'>Air</option>";
echo "<option value='Sea'>Sea</option>";
echo "</select>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>PNG Province:</label><br>";
echo "<select name='png_province' required style='width: 200px; padding: 5px;'>";
echo "<option value='National Capital District'>National Capital District</option>";
echo "<option value='Morobe'>Morobe</option>";
echo "</select>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Academic Qualifications:</label><br>";
echo "<textarea name='academic_qualifications' required style='width: 300px; height: 60px; padding: 5px;'>Test qualifications</textarea>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Motivation Letter:</label><br>";
echo "<textarea name='motivation_letter' required style='width: 300px; height: 60px; padding: 5px;'>Test motivation letter</textarea>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<input type='checkbox' name='terms_agreement' value='1' checked> I agree to terms";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Submit</button>";
echo "</div>";
echo "</form>";
echo "</div>";
?>
