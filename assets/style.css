/* Solomon Islands Scholarship Application - Custom Styles */

/* Root Variables - Grey, Red, Black Color Palette */
:root {
    --primary-color: #2c3e50;        /* Dark Grey */
    --secondary-color: #dc3545;      /* Red */
    --accent-color: #6c757d;         /* Medium Grey */
    --danger-color: #dc3545;         /* Red */
    --dark-color: #212529;           /* Black */
    --light-color: #f8f9fa;          /* Light Grey */
    --medium-grey: #495057;          /* Medium Dark Grey */
    --light-grey: #e9ecef;           /* Very Light Grey */
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, var(--primary-color), var(--medium-grey)) !important;
    box-shadow: var(--box-shadow);
}

.department-header {
    background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
    border-bottom: 2px solid var(--secondary-color);
}

.flag-img {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: 2px solid var(--secondary-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    background: linear-gradient(135deg, var(--light-grey), #ffffff) !important;
}

/* Form Styles */
.form-step {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.form-step.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid, .form-select.is-valid {
    border-color: var(--secondary-color);
}

/* Form Check Styles */
.form-check {
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-check:hover {
    background-color: rgba(220, 53, 69, 0.05);
}

.form-check-input {
    margin-top: 0.25rem;
}

.form-check-input:checked {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--medium-grey));
    box-shadow: 0 2px 4px rgba(44, 62, 80, 0.3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--medium-grey), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(44, 62, 80, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--secondary-color), #c82333);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #c82333, var(--secondary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #4e555b;
    transform: translateY(-2px);
}

/* Progress Bar */
.progress {
    background-color: var(--light-grey);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.6s ease;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-success {
    border-left-color: var(--secondary-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.alert-info {
    border-left-color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
}

/* Step Headers */
.form-step h4 {
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

/* Checkbox and Radio Groups */
.form-check-label {
    cursor: pointer;
    user-select: none;
}

/* Invalid Feedback */
.invalid-feedback {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .form-step h4 {
        font-size: 1.25rem;
    }
    
    .col-md-4, .col-md-6 {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    header h1 {
        font-size: 1.5rem;
    }
    
    .card {
        margin: 0.5rem;
    }
    
    .form-control, .form-select {
        padding: 0.5rem 0.75rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
    }
    
    #prevBtn, #nextBtn, #submitBtn {
        width: 100%;
        margin: 0.25rem 0;
    }
}

/* Print Styles */
@media print {
    .btn, .progress, header, footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .form-step {
        display: block !important;
        page-break-inside: avoid;
    }
}

/* Accessibility */
.form-control:focus,
.form-select:focus,
.btn:focus,
.form-check-input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-control, .form-select {
        border-width: 3px;
    }
    
    .btn {
        border: 2px solid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
