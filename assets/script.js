/**
 * Solomon Islands Scholarship Application - JavaScript
 * Fixed Working Version (Based on Standalone Test)
 */

// Global state
let currentStep = 1;
let totalSteps = 5;
let steps, nextBtn, prevBtn, submitBtn, progressBar;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM loaded, starting initialization...');

    // Get elements
    steps = document.querySelectorAll('.form-step');
    nextBtn = document.getElementById('nextBtn');
    prevBtn = document.getElementById('prevBtn');
    submitBtn = document.getElementById('submitBtn');
    progressBar = document.querySelector('.progress-bar');

    console.log('📋 Found elements:', {
        steps: steps.length,
        nextBtn: !!nextBtn,
        prevBtn: !!prevBtn,
        submitBtn: !!submitBtn,
        progressBar: !!progressBar
    });

    // Check if elements exist
    if (!nextBtn) {
        console.error('❌ Next button not found!');
        return;
    }

    if (steps.length === 0) {
        console.error('❌ No form steps found!');
        return;
    }

    // Update total steps based on actual count
    totalSteps = steps.length;

    // Add event listeners
    nextBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('🎯 Next button clicked!');
        handleNext();
    });

    if (prevBtn) {
        prevBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎯 Previous button clicked!');
            handlePrevious();
        });
    }

    console.log('✅ Event listeners added');

    // Initialize first step
    showStep(1);

    console.log('🎉 Initialization complete!');
});

/**
 * Show specific step
 */
function showStep(stepNumber) {
    console.log(`📍 showStep(${stepNumber}) called`);

    if (!steps) {
        console.error('❌ Steps not found!');
        return;
    }

    // Hide all steps
    steps.forEach((step, index) => {
        step.classList.remove('active');
        console.log(`👻 Hiding step ${index + 1}`);
    });

    // Show target step
    if (steps[stepNumber - 1]) {
        steps[stepNumber - 1].classList.add('active');
        currentStep = stepNumber;
        console.log(`✨ Showing step ${stepNumber}`);
    } else {
        console.error(`❌ Step ${stepNumber} not found`);
        return;
    }

    // Update buttons
    if (prevBtn) {
        prevBtn.style.display = stepNumber > 1 ? 'block' : 'none';
    }

    if (nextBtn && submitBtn) {
        if (stepNumber === totalSteps) {
            nextBtn.style.display = 'none';
            submitBtn.style.display = 'block';
        } else {
            nextBtn.style.display = 'block';
            submitBtn.style.display = 'none';
        }
    }

    // Update progress
    if (progressBar) {
        const percentage = (stepNumber / totalSteps) * 100;
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        console.log(`📊 Progress: ${percentage}%`);
    }

    console.log(`🎉 Step ${stepNumber} is now active`);
}

/**
 * Simple validation
 */
function validateStep() {
    console.log(`🔍 Validating step ${currentStep}`);

    const currentStepElement = steps[currentStep - 1];
    const requiredFields = currentStepElement.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (field.type === 'checkbox') {
            if (!field.checked) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        } else if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    // Special validation for step 2
    if (currentStep === 2) {
        const provinceCheckboxes = currentStepElement.querySelectorAll('input[name="province_of_origin[]"]');
        const isProvinceSelected = Array.from(provinceCheckboxes).some(cb => cb.checked);

        if (!isProvinceSelected) {
            const errorElement = document.getElementById('province_origin_error');
            if (errorElement) {
                errorElement.style.display = 'block';
            }
            isValid = false;
        } else {
            const errorElement = document.getElementById('province_origin_error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }
    }

    console.log(`✅ Validation result: ${isValid}`);
    return isValid;
}

/**
 * Handle next button
 */
function handleNext() {
    console.log('🚀 Next button clicked!');

    if (!validateStep()) {
        console.log('❌ Validation failed');
        return;
    }

    if (currentStep < totalSteps) {
        showStep(currentStep + 1);
    } else {
        console.log('⚠️ Already at last step');
    }
}

/**
 * Handle previous button
 */
function handlePrevious() {
    console.log('🔙 Previous button clicked!');

    if (currentStep > 1) {
        showStep(currentStep - 1);
    } else {
        console.log('⚠️ Already at first step');
    }
}

// 🎉 Fixed JavaScript file complete!
