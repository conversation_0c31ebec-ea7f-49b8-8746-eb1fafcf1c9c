/**
 * Form Navigation Script - Fixed Version
 */

console.log('🔥 FIXED SCRIPT LOADED');

// Global variables
var currentStep = 1;
var totalSteps = 3;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 FIXED SCRIPT: DOM loaded');
    
    // Find elements
    var formSteps = document.querySelectorAll('.form-step');
    var nextButton = document.getElementById('nextBtn');
    var prevButton = document.getElementById('prevBtn');
    var submitButton = document.getElementById('submitBtn');
    var progressBar = document.querySelector('.progress-bar');
    
    console.log('Found elements:', {
        steps: formSteps.length,
        nextBtn: !!nextButton,
        prevBtn: !!prevButton,
        submitBtn: !!submitButton
    });
    
    if (!nextButton) {
        alert('ERROR: Next button not found!');
        return;
    }
    
    if (formSteps.length === 0) {
        alert('ERROR: No form steps found!');
        return;
    }
    
    // Show first step
    showStep(1);
    
    // Next button click
    nextButton.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Next button clicked!');

        // Validate current step before proceeding
        if (validateStep(currentStep)) {
            if (currentStep < totalSteps) {
                showStep(currentStep + 1);
            }
        } else {
            console.log('Validation failed for step', currentStep);
        }
    });
    
    // Previous button click
    if (prevButton) {
        prevButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Previous button clicked!');
            
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        });
    }
    
    // Function to show a specific step
    function showStep(stepNumber) {
        console.log('Showing step:', stepNumber);
        
        // Hide all steps
        for (var i = 0; i < formSteps.length; i++) {
            formSteps[i].classList.remove('active');
        }
        
        // Show target step
        if (formSteps[stepNumber - 1]) {
            formSteps[stepNumber - 1].classList.add('active');
            currentStep = stepNumber;
        }
        
        // Update buttons
        if (prevButton) {
            prevButton.style.display = stepNumber > 1 ? 'block' : 'none';
        }
        
        if (nextButton && submitButton) {
            if (stepNumber === totalSteps) {
                nextButton.style.display = 'none';
                submitButton.style.display = 'block';
            } else {
                nextButton.style.display = 'block';
                submitButton.style.display = 'none';
            }
        }
        
        // Update progress bar
        if (progressBar) {
            var percentage = (stepNumber / totalSteps) * 100;
            progressBar.style.width = percentage + '%';
        }
        
        console.log('Step', stepNumber, 'is now active');
    }

    // Function to validate a step
    function validateStep(stepNumber) {
        console.log('Validating step:', stepNumber);

        var stepElement = formSteps[stepNumber - 1];
        if (!stepElement) return false;

        var requiredFields = stepElement.querySelectorAll('[required]');
        var isValid = true;

        for (var i = 0; i < requiredFields.length; i++) {
            var field = requiredFields[i];

            if (field.type === 'checkbox') {
                if (!field.checked) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            } else if (field.tagName === 'SELECT') {
                if (!field.value || field.value === '') {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            } else if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        }

        console.log('Step', stepNumber, 'validation result:', isValid);
        return isValid;
    }

    console.log('✅ FIXED SCRIPT: Initialization complete!');
});
