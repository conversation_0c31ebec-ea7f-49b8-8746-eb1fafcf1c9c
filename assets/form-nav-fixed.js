/**
 * Form Navigation Script - Fixed Version
 */

console.log('🔥 FIXED SCRIPT LOADED');

// Global variables
var currentStep = 1;
var totalSteps = 3;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 FIXED SCRIPT: DOM loaded');
    
    // Find elements
    var formSteps = document.querySelectorAll('.form-step');
    var nextButton = document.getElementById('nextBtn');
    var prevButton = document.getElementById('prevBtn');
    var submitButton = document.getElementById('submitBtn');
    var progressBar = document.querySelector('.progress-bar');
    
    console.log('Found elements:', {
        steps: formSteps.length,
        nextBtn: !!nextButton,
        prevBtn: !!prevButton,
        submitBtn: !!submitButton
    });
    
    if (!nextButton) {
        alert('ERROR: Next button not found!');
        return;
    }
    
    if (formSteps.length === 0) {
        alert('ERROR: No form steps found!');
        return;
    }
    
    // Show first step
    showStep(1);

    // Load saved form data if any
    loadFormData();

    // Save form data on input changes
    setupFormDataSaving();
    
    // Next button click
    nextButton.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Next button clicked!');

        // Validate current step before proceeding
        if (validateStep(currentStep)) {
            if (currentStep < totalSteps) {
                showStep(currentStep + 1);
            }
        } else {
            console.log('Validation failed for step', currentStep);
        }
    });
    
    // Previous button click
    if (prevButton) {
        prevButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Previous button clicked!');
            
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        });
    }
    
    // Function to show a specific step
    function showStep(stepNumber) {
        console.log('Showing step:', stepNumber);
        
        // Hide all steps
        for (var i = 0; i < formSteps.length; i++) {
            formSteps[i].classList.remove('active');
        }
        
        // Show target step
        if (formSteps[stepNumber - 1]) {
            formSteps[stepNumber - 1].classList.add('active');
            currentStep = stepNumber;
        }
        
        // Update buttons
        if (prevButton) {
            prevButton.style.display = stepNumber > 1 ? 'block' : 'none';
        }
        
        if (nextButton && submitButton) {
            if (stepNumber === totalSteps) {
                nextButton.style.display = 'none';
                submitButton.style.display = 'block';
            } else {
                nextButton.style.display = 'block';
                submitButton.style.display = 'none';
            }
        }
        
        // Update progress bar
        if (progressBar) {
            var percentage = (stepNumber / totalSteps) * 100;
            progressBar.style.width = percentage + '%';
        }
        
        console.log('Step', stepNumber, 'is now active');
    }

    // Function to validate a step
    function validateStep(stepNumber) {
        console.log('Validating step:', stepNumber);

        var stepElement = formSteps[stepNumber - 1];
        if (!stepElement) return false;

        var requiredFields = stepElement.querySelectorAll('[required]');
        var isValid = true;

        for (var i = 0; i < requiredFields.length; i++) {
            var field = requiredFields[i];

            if (field.type === 'checkbox') {
                if (!field.checked) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            } else if (field.tagName === 'SELECT') {
                if (!field.value || field.value === '') {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            } else if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        }

        console.log('Step', stepNumber, 'validation result:', isValid);
        return isValid;
    }

    // Form submission handling
    var form = document.getElementById('scholarshipForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission attempted');

            // Check if we're on the last step and terms are not agreed
            if (currentStep === totalSteps) {
                var termsCheckbox = document.getElementById('terms_agreement');
                if (termsCheckbox && !termsCheckbox.checked) {
                    e.preventDefault();

                    // Add validation styling
                    termsCheckbox.classList.add('is-invalid');

                    // Show error message
                    showTermsError();

                    // Scroll to the checkbox
                    scrollToTermsCheckbox();

                    return false;
                }
            }
        });
    }

    // Function to show terms error message
    function showTermsError() {
        // Remove any existing error message
        var existingError = document.getElementById('terms-error-message');
        if (existingError) {
            existingError.remove();
        }

        // Create error message
        var errorDiv = document.createElement('div');
        errorDiv.id = 'terms-error-message';
        errorDiv.className = 'alert alert-danger mt-3';
        errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i><strong>Submission Failed:</strong> You must agree to the terms and conditions before submitting your registration.';

        // Insert error message before the terms checkbox
        var termsContainer = document.getElementById('terms_agreement').closest('.row');
        termsContainer.parentNode.insertBefore(errorDiv, termsContainer);

        // Auto-remove error message after 10 seconds
        setTimeout(function() {
            if (errorDiv && errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 10000);
    }

    // Function to scroll to terms checkbox
    function scrollToTermsCheckbox() {
        var termsCheckbox = document.getElementById('terms_agreement');
        if (termsCheckbox) {
            // Scroll to the checkbox with some offset
            termsCheckbox.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // Add visual highlight to the checkbox area
            var checkboxContainer = termsCheckbox.closest('.form-check');
            if (checkboxContainer) {
                checkboxContainer.style.backgroundColor = '#fff3cd';
                checkboxContainer.style.border = '2px solid #ffc107';
                checkboxContainer.style.borderRadius = '8px';
                checkboxContainer.style.padding = '15px';
                checkboxContainer.style.transition = 'all 0.3s ease';

                // Remove highlight after 5 seconds
                setTimeout(function() {
                    checkboxContainer.style.backgroundColor = '';
                    checkboxContainer.style.border = '';
                    checkboxContainer.style.padding = '';
                }, 5000);
            }

            // Focus on the checkbox
            setTimeout(function() {
                termsCheckbox.focus();
            }, 500);
        }
    }

    // Add event listener to terms checkbox to remove validation styling when checked
    var termsCheckbox = document.getElementById('terms_agreement');
    if (termsCheckbox) {
        termsCheckbox.addEventListener('change', function() {
            if (this.checked) {
                this.classList.remove('is-invalid');

                // Remove error message if it exists
                var errorMessage = document.getElementById('terms-error-message');
                if (errorMessage) {
                    errorMessage.remove();
                }

                // Remove highlight
                var checkboxContainer = this.closest('.form-check');
                if (checkboxContainer) {
                    checkboxContainer.style.backgroundColor = '';
                    checkboxContainer.style.border = '';
                    checkboxContainer.style.padding = '';
                }
            }
        });
    }

    // Function to save form data to localStorage
    function saveFormData() {
        var formData = {};
        var form = document.getElementById('scholarshipForm');
        if (!form) return;

        // Get all form inputs
        var inputs = form.querySelectorAll('input, select, textarea');
        for (var i = 0; i < inputs.length; i++) {
            var input = inputs[i];
            if (input.name && input.name !== 'csrf_token') {
                if (input.type === 'checkbox') {
                    formData[input.name] = input.checked;
                } else if (input.type === 'radio') {
                    if (input.checked) {
                        formData[input.name] = input.value;
                    }
                } else {
                    formData[input.name] = input.value;
                }
            }
        }

        localStorage.setItem('scholarshipFormData', JSON.stringify(formData));
        console.log('Form data saved to localStorage');
    }

    // Function to load form data from localStorage
    function loadFormData() {
        var savedData = localStorage.getItem('scholarshipFormData');
        if (!savedData) return;

        try {
            var formData = JSON.parse(savedData);
            var form = document.getElementById('scholarshipForm');
            if (!form) return;

            // Restore form values
            for (var fieldName in formData) {
                var field = form.querySelector('[name="' + fieldName + '"]');
                if (field) {
                    if (field.type === 'checkbox') {
                        field.checked = formData[fieldName];
                    } else if (field.type === 'radio') {
                        if (field.value === formData[fieldName]) {
                            field.checked = true;
                        }
                    } else {
                        field.value = formData[fieldName];
                    }
                }
            }

            console.log('Form data loaded from localStorage');
        } catch (e) {
            console.error('Error loading form data:', e);
        }
    }

    // Function to setup form data saving on input changes
    function setupFormDataSaving() {
        var form = document.getElementById('scholarshipForm');
        if (!form) return;

        // Save data on input changes
        form.addEventListener('input', function() {
            saveFormData();
        });

        // Save data on select changes
        form.addEventListener('change', function() {
            saveFormData();
        });

        console.log('Form data saving setup complete');
    }

    // Function to clear saved form data (call this on successful submission)
    function clearSavedFormData() {
        localStorage.removeItem('scholarshipFormData');
        console.log('Saved form data cleared');
    }

    // Make clearSavedFormData available globally for use in submit handler
    window.clearSavedFormData = clearSavedFormData;

    console.log('✅ FIXED SCRIPT: Initialization complete!');
});
