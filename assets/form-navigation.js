/**
 * Form Navigation Script - Clean Version
 * This is a completely new file to avoid any caching issues
 */

console.log('🔥 NEW SCRIPT LOADED - form-navigation.js');

// Simple global variables
var currentStep = 1;
var totalSteps = 5;
var formSteps = null;
var nextButton = null;
var prevButton = null;
var submitButton = null;
var progressBar = null;

// Initialize everything when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 NEW SCRIPT: DOM loaded, initializing...');
    
    // Find all elements
    formSteps = document.querySelectorAll('.form-step');
    nextButton = document.getElementById('nextBtn');
    prevButton = document.getElementById('prevBtn');
    submitButton = document.getElementById('submitBtn');
    progressBar = document.querySelector('.progress-bar');
    
    console.log('🔍 NEW SCRIPT: Elements found:', {
        steps: formSteps.length,
        nextBtn: !!nextButton,
        prevBtn: !!prevButton,
        submitBtn: !!submitButton,
        progressBar: !!progressBar
    });
    
    // Check if we have what we need
    if (!nextButton) {
        console.error('❌ NEW SCRIPT: Next button not found!');
        alert('ERROR: Next button not found!');
        return;
    }
    
    if (formSteps.length === 0) {
        console.error('❌ NEW SCRIPT: No form steps found!');
        alert('ERROR: No form steps found!');
        return;
    }
    
    // Set total steps
    totalSteps = formSteps.length;
    console.log('📊 NEW SCRIPT: Total steps set to:', totalSteps);
    
    // Add click event to next button
    nextButton.onclick = function(e) {
        e.preventDefault();
        console.log('🎯 NEW SCRIPT: Next button clicked!');
        goToNextStep();
        return false;
    };
    
    // Add click event to previous button
    if (prevButton) {
        prevButton.onclick = function(e) {
            e.preventDefault();
            console.log('🎯 NEW SCRIPT: Previous button clicked!');
            goToPreviousStep();
            return false;
        };
    }
    
    console.log('✅ NEW SCRIPT: Event handlers attached');
    
    // Show first step
    displayStep(1);
    
    console.log('🎉 NEW SCRIPT: Initialization complete!');
});

// Function to display a specific step
function displayStep(stepNumber) {
    console.log('📍 NEW SCRIPT: displayStep(' + stepNumber + ') called');
    
    if (!formSteps) {
        console.error('❌ NEW SCRIPT: Form steps not available!');
        return;
    }
    
    // Hide all steps
    for (var i = 0; i < formSteps.length; i++) {
        formSteps[i].classList.remove('active');
        console.log('👻 NEW SCRIPT: Hiding step ' + (i + 1));
    }
    
    // Show target step
    if (formSteps[stepNumber - 1]) {
        formSteps[stepNumber - 1].classList.add('active');
        currentStep = stepNumber;
        console.log('✨ NEW SCRIPT: Showing step ' + stepNumber);
    } else {
        console.error('❌ NEW SCRIPT: Step ' + stepNumber + ' not found');
        return;
    }
    
    // Update navigation buttons
    if (prevButton) {
        prevButton.style.display = stepNumber > 1 ? 'block' : 'none';
    }
    
    if (nextButton && submitButton) {
        if (stepNumber === totalSteps) {
            nextButton.style.display = 'none';
            submitButton.style.display = 'block';
        } else {
            nextButton.style.display = 'block';
            submitButton.style.display = 'none';
        }
    }
    
    // Update progress bar
    if (progressBar) {
        var percentage = (stepNumber / totalSteps) * 100;
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        console.log('📊 NEW SCRIPT: Progress updated to ' + percentage + '%');
    }
    
    console.log('🎉 NEW SCRIPT: Step ' + stepNumber + ' is now active');
}

// Function to validate current step
function validateCurrentStep() {
    console.log('🔍 NEW SCRIPT: Validating step ' + currentStep);
    
    if (!formSteps || !formSteps[currentStep - 1]) {
        console.error('❌ NEW SCRIPT: Current step element not found');
        return false;
    }
    
    var currentStepElement = formSteps[currentStep - 1];
    var requiredFields = currentStepElement.querySelectorAll('[required]');
    var isValid = true;
    
    console.log('🔍 NEW SCRIPT: Found ' + requiredFields.length + ' required fields');
    
    // Check each required field
    for (var i = 0; i < requiredFields.length; i++) {
        var field = requiredFields[i];
        
        if (field.type === 'checkbox') {
            if (!field.checked) {
                field.classList.add('is-invalid');
                isValid = false;
                console.log('❌ NEW SCRIPT: Checkbox not checked: ' + (field.name || field.id));
            } else {
                field.classList.remove('is-invalid');
            }
        } else if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
            console.log('❌ NEW SCRIPT: Field empty: ' + (field.name || field.id));
        } else {
            field.classList.remove('is-invalid');
        }
    }
    
    // Special validation for step 2 (province checkboxes)
    if (currentStep === 2) {
        var provinceCheckboxes = currentStepElement.querySelectorAll('input[name="province_of_origin[]"]');
        var isProvinceSelected = false;
        
        for (var i = 0; i < provinceCheckboxes.length; i++) {
            if (provinceCheckboxes[i].checked) {
                isProvinceSelected = true;
                break;
            }
        }
        
        var errorElement = document.getElementById('province_origin_error');
        if (!isProvinceSelected) {
            if (errorElement) {
                errorElement.style.display = 'block';
            }
            isValid = false;
            console.log('❌ NEW SCRIPT: No province selected');
        } else {
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }
    }
    
    console.log('✅ NEW SCRIPT: Validation result: ' + isValid);
    return isValid;
}

// Function to go to next step
function goToNextStep() {
    console.log('🚀 NEW SCRIPT: goToNextStep() called');
    
    if (!validateCurrentStep()) {
        console.log('❌ NEW SCRIPT: Validation failed');
        return;
    }
    
    if (currentStep < totalSteps) {
        displayStep(currentStep + 1);
    } else {
        console.log('⚠️ NEW SCRIPT: Already at last step');
    }
}

// Function to go to previous step
function goToPreviousStep() {
    console.log('🔙 NEW SCRIPT: goToPreviousStep() called');
    
    if (currentStep > 1) {
        displayStep(currentStep - 1);
    } else {
        console.log('⚠️ NEW SCRIPT: Already at first step');
    }
}

// Test function that can be called manually
function testNavigation() {
    console.log('🧪 NEW SCRIPT: Testing navigation...');
    for (var i = 1; i <= totalSteps; i++) {
        setTimeout(function(step) {
            return function() {
                console.log('🎯 NEW SCRIPT: Auto-navigating to step ' + step);
                displayStep(step);
            };
        }(i), i * 1000);
    }
}

console.log('📜 NEW SCRIPT: form-navigation.js loaded completely!');
